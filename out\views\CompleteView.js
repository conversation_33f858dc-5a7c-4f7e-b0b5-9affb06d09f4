"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompleteView = void 0;
const vscode = __importStar(require("vscode"));
class CompleteView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title
        const titleItem = new vscode.TreeItem('✅ Process Complete', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Time tracking data generated successfully';
        titleItem.iconPath = new vscode.ThemeIcon('check-all');
        items.push(titleItem);
        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        // Statistics
        const statsItem = new vscode.TreeItem('📊 Statistics', vscode.TreeItemCollapsibleState.None);
        statsItem.description = 'View execution summary';
        statsItem.iconPath = new vscode.ThemeIcon('graph');
        items.push(statsItem);
        const changesItem = new vscode.TreeItem(`Changes Applied: ${state.changes.length}`, vscode.TreeItemCollapsibleState.None);
        changesItem.iconPath = new vscode.ThemeIcon('edit');
        items.push(changesItem);
        const timeItem = new vscode.TreeItem('Time Simulated: X hours', vscode.TreeItemCollapsibleState.None);
        timeItem.iconPath = new vscode.ThemeIcon('clock');
        items.push(timeItem);
        // Actions
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const newSessionItem = new vscode.TreeItem('🔄 Start New Session', vscode.TreeItemCollapsibleState.None);
        newSessionItem.description = 'Begin another time tracking session';
        newSessionItem.iconPath = new vscode.ThemeIcon('refresh');
        newSessionItem.command = {
            command: 'wakatimerpro.startNewSession',
            title: 'Start New Session'
        };
        items.push(newSessionItem);
        const exportItem = new vscode.TreeItem('💾 Export Report', vscode.TreeItemCollapsibleState.None);
        exportItem.description = 'Save execution report';
        exportItem.iconPath = new vscode.ThemeIcon('save');
        exportItem.command = {
            command: 'wakatimerpro.exportReport',
            title: 'Export Report'
        };
        items.push(exportItem);
        // Register commands
        this.registerCommands();
        return items;
    }
    registerCommands() {
        vscode.commands.registerCommand('wakatimerpro.startNewSession', () => {
            this.stateManager.resetState();
        });
        vscode.commands.registerCommand('wakatimerpro.exportReport', () => {
            vscode.window.showInformationMessage('Export functionality will be implemented here');
        });
    }
}
exports.CompleteView = CompleteView;
//# sourceMappingURL=CompleteView.js.map