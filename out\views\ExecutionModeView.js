"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionModeView = void 0;
const vscode = __importStar(require("vscode"));
class ExecutionModeView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        // Title
        const titleItem = new vscode.TreeItem('Choose Execution Mode', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Select how to generate changes';
        titleItem.iconPath = new vscode.ThemeIcon('gear');
        items.push(titleItem);
        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        // Simple mode option
        const simpleItem = new vscode.TreeItem('🤖 Simple Mode', vscode.TreeItemCollapsibleState.None);
        simpleItem.description = 'Automated with VSCode AI';
        simpleItem.iconPath = new vscode.ThemeIcon('robot');
        simpleItem.tooltip = 'Uses VSCode\'s built-in Language Model API (GPT-4.1) to automatically generate and apply changes. Fully automated process.';
        simpleItem.command = {
            command: 'wakatimerpro.selectExecutionMode',
            title: 'Select Simple Mode',
            arguments: ['simple']
        };
        items.push(simpleItem);
        // Advanced mode option
        const advancedItem = new vscode.TreeItem('⚙️ Advanced Mode', vscode.TreeItemCollapsibleState.None);
        advancedItem.description = 'Manual with external AI';
        advancedItem.iconPath = new vscode.ThemeIcon('settings');
        advancedItem.tooltip = 'Generates prompts for you to use with external AI tools (Claude, ChatGPT, etc.). You copy the prompt, get the response, and paste it back for processing.';
        advancedItem.command = {
            command: 'wakatimerpro.selectExecutionMode',
            title: 'Select Advanced Mode',
            arguments: ['advanced']
        };
        items.push(advancedItem);
        // Mode descriptions
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const infoItem = new vscode.TreeItem('ℹ️ Mode Information', vscode.TreeItemCollapsibleState.None);
        infoItem.description = 'Click for detailed comparison';
        infoItem.iconPath = new vscode.ThemeIcon('info');
        infoItem.command = {
            command: 'wakatimerpro.showModeInfo',
            title: 'Show Mode Information'
        };
        items.push(infoItem);
        // Back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to time configuration';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['time-config']
        };
        items.push(backItem);
        return items;
    }
}
exports.ExecutionModeView = ExecutionModeView;
//# sourceMappingURL=ExecutionModeView.js.map