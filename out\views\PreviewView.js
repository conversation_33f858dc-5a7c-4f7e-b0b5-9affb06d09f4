"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreviewView = void 0;
const vscode = __importStar(require("vscode"));
const WakatimerOrchestrator_1 = require("../services/WakatimerOrchestrator");
class PreviewView {
    stateManager;
    orchestrator;
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.orchestrator = new WakatimerOrchestrator_1.WakatimerOrchestrator(stateManager);
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title
        const titleItem = new vscode.TreeItem('Changes Ready', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Review before execution';
        titleItem.iconPath = new vscode.ThemeIcon('check');
        items.push(titleItem);
        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        // Status
        const statusItem = new vscode.TreeItem('✅ Changes Verified', vscode.TreeItemCollapsibleState.None);
        statusItem.description = `${state.changes.length} changes ready`;
        statusItem.iconPath = new vscode.ThemeIcon('verified');
        items.push(statusItem);
        // Preview button
        const previewItem = new vscode.TreeItem('👁️ Preview Changes', vscode.TreeItemCollapsibleState.None);
        previewItem.description = 'View diffs in editor';
        previewItem.iconPath = new vscode.ThemeIcon('diff');
        previewItem.command = {
            command: 'wakatimerpro.previewChanges',
            title: 'Preview Changes'
        };
        items.push(previewItem);
        // Summary
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const summaryItem = new vscode.TreeItem('📊 Summary', vscode.TreeItemCollapsibleState.None);
        summaryItem.description = 'Execution details';
        summaryItem.iconPath = new vscode.ThemeIcon('graph');
        items.push(summaryItem);
        // Start execution button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const startItem = new vscode.TreeItem('🚀 Start Execution', vscode.TreeItemCollapsibleState.None);
        startItem.description = 'Begin applying changes';
        startItem.iconPath = new vscode.ThemeIcon('play');
        startItem.command = {
            command: 'wakatimerpro.startExecution',
            title: 'Start Execution'
        };
        items.push(startItem);
        // Back button
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to processing';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['processing']
        };
        items.push(backItem);
        return items;
    }
    generatePreviewContent(changes) {
        const lines = [
            '# Wakatimer Pro - Changes Preview',
            '',
            `Total changes: ${changes.length}`,
            '',
            '## Changes to be applied:',
            ''
        ];
        changes.forEach((change, index) => {
            lines.push(`### Change ${index + 1}: ${change.description}`);
            lines.push(`File: ${change.file}`);
            lines.push(`Type: ${change.type}`);
            lines.push('');
            lines.push('```diff');
            lines.push(change.diff);
            lines.push('```');
            lines.push('');
        });
        return lines.join('\n');
    }
}
exports.PreviewView = PreviewView;
//# sourceMappingURL=PreviewView.js.map