{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,IAAY,aAKX;AALD,WAAY,aAAa;IACrB,8BAAa,CAAA;IACb,oCAAmB,CAAA;IACnB,gCAAe,CAAA;IACf,sCAAqB,CAAA;AACzB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAWD,MAAa,YAAY;IACb,MAAM,CAAC,QAAQ,CAAe;IAE/B,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAqB,EAAE,OAAgB;QACrD,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE9D,gBAAgB;QAChB,OAAO,CAAC,KAAK,CAAC,mBAAmB,OAAO,IAAI,OAAO,GAAG,EAAE,YAAY,CAAC,CAAC;QAEtE,gCAAgC;QAChC,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAEtC,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,OAAe,EAAE,OAAgB;QACrD,iBAAiB;QACjB,IAAI,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACxE,OAAO;gBACH,OAAO,EAAE,2BAA2B;gBACpC,QAAQ,EAAE,aAAa,CAAC,KAAK;gBAC7B,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,mFAAmF;gBAC5F,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK;aACrB,CAAC;QACN,CAAC;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnE,OAAO;gBACH,OAAO,EAAE,6BAA6B;gBACtC,QAAQ,EAAE,aAAa,CAAC,KAAK;gBAC7B,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,qDAAqD;gBAC9D,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;aACpB,CAAC;QACN,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACtE,OAAO;gBACH,OAAO,EAAE,mBAAmB;gBAC5B,QAAQ,EAAE,aAAa,CAAC,KAAK;gBAC7B,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,8DAA8D;gBACvE,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,KAAK;aACrB,CAAC;QACN,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnE,OAAO;gBACH,OAAO,EAAE,qBAAqB;gBAC9B,QAAQ,EAAE,aAAa,CAAC,OAAO;gBAC/B,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,mDAAmD;gBAC5D,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,IAAI;aACpB,CAAC;QACN,CAAC;QAED,iBAAiB;QACjB,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClE,OAAO;gBACH,OAAO,EAAE,iCAAiC;gBAC1C,QAAQ,EAAE,aAAa,CAAC,OAAO;gBAC/B,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,gDAAgD;gBACzD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;aACpB,CAAC;QACN,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,OAAO;gBACH,OAAO,EAAE,0BAA0B;gBACnC,QAAQ,EAAE,aAAa,CAAC,OAAO;gBAC/B,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,yDAAyD;gBAClE,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK;aACrB,CAAC;QACN,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAChE,OAAO;gBACH,OAAO,EAAE,kBAAkB;gBAC3B,QAAQ,EAAE,aAAa,CAAC,OAAO;gBAC/B,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,sCAAsC;gBAC/C,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,IAAI;aACpB,CAAC;QACN,CAAC;QAED,gBAAgB;QAChB,OAAO;YACH,OAAO,EAAE,8BAA8B;YACvC,QAAQ,EAAE,aAAa,CAAC,KAAK;YAC7B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,KAAK;SACrB,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,SAAoB;QAC9C,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;QAED,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvB,IAAI,UAAiF,CAAC;QAEtF,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,aAAa,CAAC,IAAI;gBACnB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBAClD,MAAM;YACV,KAAK,aAAa,CAAC,OAAO;gBACtB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC9C,MAAM;YACV,KAAK,aAAa,CAAC,KAAK,CAAC;YACzB,KAAK,aAAa,CAAC,QAAQ;gBACvB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC5C,MAAM;QACd,CAAC;QAED,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO;YACjC,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,gBAAgB,SAAS,CAAC,OAAO,EAAE;YACzD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC;QAExB,MAAM,UAAU,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,yBAAyB,CAAC,KAAqB,EAAE,OAAgB;QAC7D,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpE,yDAAyD;QACzD,MAAM,QAAQ,GAAG;YACb;gBACI,OAAO,EAAE,6BAA6B;gBACtC,OAAO,EAAE,+EAA+E;aAC3F;YACD;gBACI,OAAO,EAAE,wBAAwB;gBACjC,OAAO,EAAE,kDAAkD;aAC9D;YACD;gBACI,OAAO,EAAE,2BAA2B;gBACpC,OAAO,EAAE,mDAAmD;aAC/D;YACD;gBACI,OAAO,EAAE,UAAU;gBACnB,OAAO,EAAE,4CAA4C;aACxD;YACD;gBACI,OAAO,EAAE,qBAAqB;gBAC9B,OAAO,EAAE,kEAAkE;aAC9E;SACJ,CAAC;QAEF,KAAK,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,QAAQ,EAAE,CAAC;YAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7B,OAAO,OAAO,CAAC;YACnB,CAAC;QACL,CAAC;QAED,OAAO,oBAAoB,OAAO,CAAC,CAAC,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE,CAAC;IAClF,CAAC;IAED,QAAQ,CAAC,KAAqB,EAAE,OAAgB,EAAE,cAAoB;QAClE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,MAAM,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAE/D,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS,wBAAwB,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACtF,OAAO,CAAC,KAAK,CAAC,YAAY,YAAY,EAAE,CAAC,CAAC;QAE1C,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;CACJ;AA5MD,oCA4MC"}