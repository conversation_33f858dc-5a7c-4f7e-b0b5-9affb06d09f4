import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';

export class TimeConfigView {
    private stateManager: StateManager;
    private showAdvanced: boolean = false;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('Configure Time Settings', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Set duration and schedule';
        titleItem.iconPath = new vscode.ThemeIcon('clock');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Days configuration
        const daysItem = new vscode.TreeItem(`📅 Days: ${state.timeConfig.days}`, vscode.TreeItemCollapsibleState.None);
        daysItem.description = 'Number of days to simulate';
        daysItem.iconPath = new vscode.ThemeIcon('calendar');
        daysItem.command = {
            command: 'wakatimerpro.configureDays',
            title: 'Configure Days'
        };
        items.push(daysItem);

        // Hours per day configuration
        const hoursItem = new vscode.TreeItem(`⏰ Hours per day: ${state.timeConfig.hoursPerDay}`, vscode.TreeItemCollapsibleState.None);
        hoursItem.description = 'Working hours per day';
        hoursItem.iconPath = new vscode.ThemeIcon('watch');
        hoursItem.command = {
            command: 'wakatimerpro.configureHours',
            title: 'Configure Hours'
        };
        items.push(hoursItem);

        // Advanced options (only show if more than 1 day)
        if (state.timeConfig.days > 1) {
            const advancedItem = new vscode.TreeItem(
                this.showAdvanced ? '🔽 Advanced Options' : '▶️ Advanced Options',
                vscode.TreeItemCollapsibleState.None
            );
            advancedItem.description = 'Customize hours per day';
            advancedItem.iconPath = new vscode.ThemeIcon('settings-gear');
            advancedItem.command = {
                command: 'wakatimerpro.toggleTimeAdvanced',
                title: 'Toggle Advanced Time Options'
            };
            items.push(advancedItem);

            if (this.showAdvanced) {
                items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
                
                const headerItem = new vscode.TreeItem('Custom Hours per Day:', vscode.TreeItemCollapsibleState.None);
                headerItem.iconPath = new vscode.ThemeIcon('list-unordered');
                items.push(headerItem);

                // Show each day with custom hours
                for (let day = 1; day <= state.timeConfig.days; day++) {
                    const customHours = state.timeConfig.customHours[day] || state.timeConfig.hoursPerDay;
                    const dayItem = new vscode.TreeItem(
                        `Day ${day}: ${customHours} hours`,
                        vscode.TreeItemCollapsibleState.None
                    );
                    dayItem.description = `Click to modify`;
                    dayItem.iconPath = new vscode.ThemeIcon('edit');
                    dayItem.command = {
                        command: 'wakatimerpro.configureCustomHours',
                        title: 'Configure Custom Hours',
                        arguments: [day]
                    };
                    items.push(dayItem);
                }
            }
        }

        // Summary
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const totalHours = this.calculateTotalHours(state.timeConfig);
        const totalChanges = Math.floor((totalHours * 60) / 1.5); // 90 seconds per change
        
        const summaryItem = new vscode.TreeItem('📊 Summary', vscode.TreeItemCollapsibleState.None);
        summaryItem.description = `${totalHours}h total, ~${totalChanges} changes`;
        summaryItem.iconPath = new vscode.ThemeIcon('graph');
        summaryItem.tooltip = `Total hours: ${totalHours}\nEstimated changes: ${totalChanges}\n(One change every 90 seconds)`;
        items.push(summaryItem);

        // Next button
        const nextItem = new vscode.TreeItem('✅ Next', vscode.TreeItemCollapsibleState.None);
        nextItem.description = 'Continue to execution mode';
        nextItem.iconPath = new vscode.ThemeIcon('arrow-right');
        nextItem.command = {
            command: 'wakatimerpro.proceedFromTimeConfig',
            title: 'Proceed from Time Config'
        };
        items.push(nextItem);

        // Back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to previous step';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: [state.mode === 'copy-and-edit' ? 'file-copy' : 'directory-selection']
        };
        items.push(backItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private calculateTotalHours(timeConfig: any): number {
        let total = 0;
        for (let day = 1; day <= timeConfig.days; day++) {
            total += timeConfig.customHours[day] || timeConfig.hoursPerDay;
        }
        return total;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.configureDays', async () => {
            const input = await vscode.window.showInputBox({
                prompt: 'Enter number of days (1-30)',
                value: this.stateManager.getState().timeConfig.days.toString(),
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num < 1 || num > 30) {
                        return 'Please enter a number between 1 and 30';
                    }
                    return null;
                }
            });

            if (input) {
                const days = parseInt(input);
                this.stateManager.setTimeConfig({ days });
            }
        });

        vscode.commands.registerCommand('wakatimerpro.configureHours', async () => {
            const input = await vscode.window.showInputBox({
                prompt: 'Enter hours per day (1-24)',
                value: this.stateManager.getState().timeConfig.hoursPerDay.toString(),
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num < 1 || num > 24) {
                        return 'Please enter a number between 1 and 24';
                    }
                    return null;
                }
            });

            if (input) {
                const hoursPerDay = parseInt(input);
                this.stateManager.setTimeConfig({ hoursPerDay });
            }
        });

        vscode.commands.registerCommand('wakatimerpro.toggleTimeAdvanced', () => {
            this.showAdvanced = !this.showAdvanced;
        });

        vscode.commands.registerCommand('wakatimerpro.configureCustomHours', async (day: number) => {
            const state = this.stateManager.getState();
            const currentHours = state.timeConfig.customHours[day] || state.timeConfig.hoursPerDay;
            
            const input = await vscode.window.showInputBox({
                prompt: `Enter hours for day ${day} (0-24)`,
                value: currentHours.toString(),
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num < 0 || num > 24) {
                        return 'Please enter a number between 0 and 24';
                    }
                    return null;
                }
            });

            if (input) {
                const hours = parseInt(input);
                const customHours = { ...state.timeConfig.customHours };
                customHours[day] = hours;
                this.stateManager.setTimeConfig({ customHours });
            }
        });

        vscode.commands.registerCommand('wakatimerpro.proceedFromTimeConfig', () => {
            this.stateManager.setCurrentStep('execution-mode');
        });
    }
}
