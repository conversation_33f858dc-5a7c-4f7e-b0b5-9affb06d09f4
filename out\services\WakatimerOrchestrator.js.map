{"version": 3, "file": "WakatimerOrchestrator.js", "sourceRoot": "", "sources": ["../../src/services/WakatimerOrchestrator.ts"], "names": [], "mappings": ";;;AAEA,6CAA0C;AAC1C,uDAAoD;AACpD,6CAAwD;AACxD,yDAAsD;AAEtD,MAAa,qBAAqB;IACtB,YAAY,CAAe;IAC3B,UAAU,CAAa;IACvB,eAAe,CAAkB;IACjC,UAAU,CAAa;IACvB,gBAAgB,CAAmB;IACnC,WAAW,GAAY,KAAK,CAAC;IAErC,YAAY,YAA0B;QAClC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAE3C,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEjC,iCAAiC;YACjC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC,CAAC;YAEpG,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAC3D,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,8FAA8F,CAAC,CAAC;YACpH,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAEjG,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,eAAe,CAAC;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAErF,mCAAmC;YACnC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC,CAAC;YAEhG,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CACrD,gBAAgB,CAAC,IAAI,EACrB,EAAE,EAAE,4DAA4D;YAChE,KAAK,CAAC,UAAU,EAChB,CAAC,KAAa,EAAE,EAAE;gBACd,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7C,CAAC,CACJ,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,4BAA4B,CAAC,CAAC;YACvE,CAAC;YAED,qCAAqC;YACrC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC,CAAC;YAEpG,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEvE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,oCAAoC,CAAC,CAAC;YACxE,CAAC;YAED,4CAA4C;YAC5C,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC,CAAC;YAEnG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,8BAA8B;YAC9B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAEtF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC7B,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAE3C,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEjC,oCAAoC;YACpC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAEjG,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,eAAe,CAAC;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAErF,0BAA0B;YAC1B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,sCAAsC,EAAE,CAAC,CAAC;YAEvG,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;YAEnF,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC,CAAC;YAEhG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAEvC,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,KAAK,CAAC;QAChB,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC7B,CAAC;IACL,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEjC,kCAAkC;YAClC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAEzF,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEvE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,oCAAoC,CAAC,CAAC;YACxE,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAEjG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAE3C,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAE9C,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,eAAe,CAAC;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CACnD,KAAK,CAAC,OAAO,EACb,gBAAgB,EAChB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACtB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7D,CAAC,EACD,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAC9C,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,6BAA6B,CAAC,CAAC;YAC9E,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACvF,CAAC;IACL,CAAC;IAED,cAAc;QACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;IAED,eAAe;QACX,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;IACnC,CAAC;IAED,aAAa;QACT,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAEO,0BAA0B,CAAC,gBAAqB,EAAE,UAAe;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;QAE5D,OAAO;;;EAGb,gBAAgB,CAAC,IAAI;;;;eAIR,gBAAgB,CAAC,OAAO,CAAC,UAAU;aACrC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;cAC5C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;qBAIhG,eAAe;;;;;;;;;gBASpB,UAAU,CAAC,IAAI;mBACZ,UAAU,CAAC,WAAW;kBACvB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4EA0BoB,CAAC;IACzE,CAAC;IAEO,mBAAmB,CAAC,UAAe;QACvC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YAC9C,KAAK,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC;QACnE,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AA9RD,sDA8RC"}