import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { StateManager } from '../core/StateManager';

export class DirectorySelectionView {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title based on mode
        const titleText = state.mode === 'edit-in-place' 
            ? 'Select Working Directory' 
            : 'Select Destination Directory';
        
        const titleItem = new vscode.TreeItem(titleText, vscode.TreeItemCollapsibleState.None);
        titleItem.description = state.mode === 'edit-in-place' 
            ? 'Choose the directory to work with'
            : 'Choose where to copy files to';
        titleItem.iconPath = new vscode.ThemeIcon('folder');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Current workspace info
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        
        if (workspaceFolder) {
            const currentDirItem = new vscode.TreeItem('📂 Current Directory', vscode.TreeItemCollapsibleState.None);
            currentDirItem.description = workspaceFolder.uri.fsPath;
            currentDirItem.iconPath = new vscode.ThemeIcon('folder-opened');
            currentDirItem.tooltip = `Current workspace: ${workspaceFolder.uri.fsPath}`;
            items.push(currentDirItem);

            // Check if directory is empty (for copy mode)
            if (state.mode === 'copy-and-edit') {
                const isEmpty = await this.isDirectoryEmpty(workspaceFolder.uri.fsPath);
                if (!isEmpty) {
                    const warningItem = new vscode.TreeItem('⚠️ Warning', vscode.TreeItemCollapsibleState.None);
                    warningItem.description = 'Directory is not empty';
                    warningItem.iconPath = new vscode.ThemeIcon('warning');
                    warningItem.tooltip = 'The destination directory should be empty for copy mode';
                    items.push(warningItem);
                }
            }

            // Next button (disabled if copy mode and directory not empty)
            const canProceed = state.mode === 'edit-in-place' || await this.isDirectoryEmpty(workspaceFolder.uri.fsPath);
            
            const nextItem = new vscode.TreeItem(canProceed ? '✅ Next' : '❌ Next (Disabled)', vscode.TreeItemCollapsibleState.None);
            nextItem.description = canProceed ? 'Continue to next step' : 'Directory must be empty';
            nextItem.iconPath = new vscode.ThemeIcon(canProceed ? 'arrow-right' : 'error');
            
            if (canProceed) {
                nextItem.command = {
                    command: 'wakatimerpro.proceedFromDirectory',
                    title: 'Next',
                    arguments: [workspaceFolder.uri.fsPath]
                };
            }
            items.push(nextItem);

        } else {
            // No workspace open
            const noWorkspaceItem = new vscode.TreeItem('❌ No Folder Open', vscode.TreeItemCollapsibleState.None);
            noWorkspaceItem.description = 'Please open a folder first';
            noWorkspaceItem.iconPath = new vscode.ThemeIcon('error');
            items.push(noWorkspaceItem);

            // Open folder button
            const openFolderItem = new vscode.TreeItem('📁 Open Folder', vscode.TreeItemCollapsibleState.None);
            openFolderItem.description = 'Select a folder to work with';
            openFolderItem.iconPath = new vscode.ThemeIcon('folder-opened');
            openFolderItem.command = {
                command: 'wakatimerpro.openFolderDialog',
                title: 'Open Folder'
            };
            items.push(openFolderItem);
        }

        // Spacer and back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to mode selection';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['mode-selection']
        };
        items.push(backItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private async isDirectoryEmpty(dirPath: string): Promise<boolean> {
        try {
            const files = await fs.promises.readdir(dirPath);
            return files.length === 0;
        } catch (error) {
            return false;
        }
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.proceedFromDirectory', (directoryPath: string) => {
            const state = this.stateManager.getState();
            
            if (state.mode === 'edit-in-place') {
                this.stateManager.setDirectories(null, directoryPath);
                this.stateManager.setCurrentStep('time-config');
            } else {
                this.stateManager.setDirectories(null, directoryPath);
                this.stateManager.setCurrentStep('file-copy');
            }
        });
    }

    async handleFolderSelection(folderUri: vscode.Uri): Promise<void> {
        // This will be called when a folder is selected via the open dialog
        const state = this.stateManager.getState();
        
        if (state.mode === 'copy-and-edit') {
            const isEmpty = await this.isDirectoryEmpty(folderUri.fsPath);
            if (!isEmpty) {
                vscode.window.showWarningMessage('The selected directory is not empty. Please choose an empty directory for copy mode.');
                return;
            }
        }

        // Update the workspace
        await vscode.commands.executeCommand('vscode.openFolder', folderUri);
    }
}
