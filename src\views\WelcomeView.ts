import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';

export class WelcomeView {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];

        // Extension title
        const titleItem = new vscode.TreeItem('Wakatimer Pro', vscode.TreeItemCollapsibleState.None);
        titleItem.description = '';
        titleItem.iconPath = new vscode.ThemeIcon('clock');
        titleItem.tooltip = 'Professional retroactive time tracking data generator';
        items.push(titleItem);

        // Version info
        const versionItem = new vscode.TreeItem('Version 1.0.0', vscode.TreeItemCollapsibleState.None);
        versionItem.description = '';
        versionItem.iconPath = new vscode.ThemeIcon('info');
        versionItem.tooltip = 'Current extension version';
        items.push(versionItem);

        // Spacer
        const spacerItem = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        spacerItem.description = '';
        items.push(spacerItem);

        // Start button
        const startItem = new vscode.TreeItem('🚀 Start', vscode.TreeItemCollapsibleState.None);
        startItem.description = 'Begin time tracking setup';
        startItem.iconPath = new vscode.ThemeIcon('play');
        startItem.tooltip = 'Click to start the time tracking setup process';
        startItem.command = {
            command: 'wakatimerpro.start',
            title: 'Start',
            arguments: []
        };
        items.push(startItem);

        // Register the start command if not already registered
        this.registerStartCommand();

        return items;
    }

    private registerStartCommand(): void {
        // Register the start command
        vscode.commands.registerCommand('wakatimerpro.start', () => {
            this.stateManager.setCurrentStep('mode-selection');
        });
    }
}
