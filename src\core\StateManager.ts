import * as vscode from 'vscode';

export interface AppState {
    currentStep: 'welcome' | 'mode-selection' | 'directory-selection' | 'file-copy' | 'time-config' | 'execution-mode' | 'processing' | 'preview' | 'execution' | 'complete';
    mode: 'edit-in-place' | 'copy-and-edit' | null;
    sourceDirectory: string | null;
    destinationDirectory: string | null;
    selectedFiles: string[];
    timeConfig: {
        days: number;
        hoursPerDay: number;
        customHours: { [day: number]: number };
    };
    executionMode: 'simple' | 'advanced' | null;
    changes: any[];
    isProcessing: boolean;
    error: string | null;
    progress: {
        current: number;
        total: number;
        stage: string;
    };
}

export class StateManager {
    private state: AppState;
    private context: vscode.ExtensionContext;
    private onStateChangeEmitter = new vscode.EventEmitter<AppState>();
    public readonly onStateChange = this.onStateChangeEmitter.event;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.state = this.getInitialState();
        this.loadState();
    }

    private getInitialState(): AppState {
        return {
            currentStep: 'welcome',
            mode: null,
            sourceDirectory: null,
            destinationDirectory: null,
            selectedFiles: [],
            timeConfig: {
                days: 1,
                hoursPerDay: 8,
                customHours: {}
            },
            executionMode: null,
            changes: [],
            isProcessing: false,
            error: null,
            progress: {
                current: 0,
                total: 0,
                stage: ''
            }
        };
    }

    private loadState(): void {
        const savedState = this.context.globalState.get<Partial<AppState>>('wakatimerpro.state');
        if (savedState) {
            this.state = { ...this.state, ...savedState };
        }
    }

    private saveState(): void {
        this.context.globalState.update('wakatimerpro.state', this.state);
    }

    public getState(): AppState {
        return { ...this.state };
    }

    public updateState(updates: Partial<AppState>): void {
        this.state = { ...this.state, ...updates };
        this.saveState();
        this.onStateChangeEmitter.fire(this.state);
    }

    public resetState(): void {
        this.state = this.getInitialState();
        this.saveState();
        this.onStateChangeEmitter.fire(this.state);
    }

    public setCurrentStep(step: AppState['currentStep']): void {
        this.updateState({ currentStep: step });
    }

    public setMode(mode: AppState['mode']): void {
        this.updateState({ mode });
    }

    public setDirectories(source: string | null, destination: string | null): void {
        this.updateState({ 
            sourceDirectory: source, 
            destinationDirectory: destination 
        });
    }

    public setSelectedFiles(files: string[]): void {
        this.updateState({ selectedFiles: files });
    }

    public setTimeConfig(config: Partial<AppState['timeConfig']>): void {
        this.updateState({ 
            timeConfig: { ...this.state.timeConfig, ...config } 
        });
    }

    public setExecutionMode(mode: AppState['executionMode']): void {
        this.updateState({ executionMode: mode });
    }

    public setChanges(changes: any[]): void {
        this.updateState({ changes });
    }

    public setProcessing(isProcessing: boolean): void {
        this.updateState({ isProcessing });
    }

    public setError(error: string | null): void {
        this.updateState({ error });
    }

    public setProgress(progress: Partial<AppState['progress']>): void {
        this.updateState({ 
            progress: { ...this.state.progress, ...progress } 
        });
    }
}
