"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingView = void 0;
const vscode = __importStar(require("vscode"));
class ProcessingView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title
        const titleItem = new vscode.TreeItem('Processing...', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Generating changes';
        titleItem.iconPath = new vscode.ThemeIcon('loading~spin');
        items.push(titleItem);
        // Progress indicator
        if (state.progress.total > 0) {
            const progressItem = new vscode.TreeItem(`Progress: ${state.progress.current}/${state.progress.total}`, vscode.TreeItemCollapsibleState.None);
            progressItem.description = state.progress.stage;
            progressItem.iconPath = new vscode.ThemeIcon('graph');
            items.push(progressItem);
        }
        // Current stage
        if (state.progress.stage) {
            const stageItem = new vscode.TreeItem(state.progress.stage, vscode.TreeItemCollapsibleState.None);
            stageItem.iconPath = new vscode.ThemeIcon('gear');
            items.push(stageItem);
        }
        // Error handling
        if (state.error) {
            items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
            const errorItem = new vscode.TreeItem('❌ Error Occurred', vscode.TreeItemCollapsibleState.None);
            errorItem.description = state.error;
            errorItem.iconPath = new vscode.ThemeIcon('error');
            items.push(errorItem);
            // Retry button
            const retryItem = new vscode.TreeItem('🔄 Retry', vscode.TreeItemCollapsibleState.None);
            retryItem.description = 'Try again';
            retryItem.iconPath = new vscode.ThemeIcon('refresh');
            retryItem.command = {
                command: 'wakatimerpro.retryProcessing',
                title: 'Retry Processing'
            };
            items.push(retryItem);
            // Continue anyway button (if applicable)
            const continueItem = new vscode.TreeItem('⚠️ Continue Anyway', vscode.TreeItemCollapsibleState.None);
            continueItem.description = 'Proceed with partial results';
            continueItem.iconPath = new vscode.ThemeIcon('warning');
            continueItem.command = {
                command: 'wakatimerpro.continueWithError',
                title: 'Continue with Error'
            };
            items.push(continueItem);
        }
        // Register commands
        this.registerCommands();
        return items;
    }
    registerCommands() {
        vscode.commands.registerCommand('wakatimerpro.retryProcessing', () => {
            this.stateManager.setError(null);
            // Restart processing logic here
        });
        vscode.commands.registerCommand('wakatimerpro.continueWithError', () => {
            this.stateManager.setError(null);
            this.stateManager.setCurrentStep('preview');
        });
    }
}
exports.ProcessingView = ProcessingView;
//# sourceMappingURL=ProcessingView.js.map