import * as assert from 'assert';
import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';
import { DiffParser } from '../services/DiffParser';
import { ProjectAnalyzer } from '../services/ProjectAnalyzer';

suite('Wakatimer Pro Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('StateManager initialization', () => {
        const mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve()
            }
        } as any;

        const stateManager = new StateManager(mockContext);
        const state = stateManager.getState();

        assert.strictEqual(state.currentStep, 'welcome');
        assert.strictEqual(state.mode, null);
        assert.strictEqual(state.timeConfig.days, 1);
        assert.strictEqual(state.timeConfig.hoursPerDay, 8);
    });

    test('DiffParser parses valid changes', () => {
        const parser = new DiffParser();
        const mockResponse = `
<changes>
<change id="1" file="test.js" type="modification">
<description>Add console.log statement</description>
<diff>
--- a/test.js
+++ b/test.js
@@ -1,3 +1,4 @@
 function test() {
+    console.log('Hello World');
     return true;
 }
</diff>
</change>
</changes>
        `;

        const changes = parser.parseChanges(mockResponse);
        assert.strictEqual(changes.length, 1);
        assert.strictEqual(changes[0].id, '1');
        assert.strictEqual(changes[0].file, 'test.js');
        assert.strictEqual(changes[0].type, 'modification');
        assert.strictEqual(changes[0].description, 'Add console.log statement');
    });

    test('DiffParser handles invalid format', () => {
        const parser = new DiffParser();
        const invalidResponse = 'This is not a valid response';

        assert.throws(() => {
            parser.parseChanges(invalidResponse);
        }, /No changes found in LLM response/);
    });

    test('ProjectAnalyzer filters ignored directories', async () => {
        const analyzer = new ProjectAnalyzer();
        
        // Test the private method through reflection (for testing purposes)
        const shouldIgnore = (analyzer as any).shouldIgnoreDirectory;
        
        assert.strictEqual(shouldIgnore('node_modules'), true);
        assert.strictEqual(shouldIgnore('.git'), true);
        assert.strictEqual(shouldIgnore('.vscode'), true);
        assert.strictEqual(shouldIgnore('src'), false);
        assert.strictEqual(shouldIgnore('lib'), false);
    });

    test('StateManager updates state correctly', () => {
        const mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve()
            }
        } as any;

        const stateManager = new StateManager(mockContext);
        
        stateManager.setMode('edit-in-place');
        stateManager.setTimeConfig({ days: 5, hoursPerDay: 6 });
        
        const state = stateManager.getState();
        assert.strictEqual(state.mode, 'edit-in-place');
        assert.strictEqual(state.timeConfig.days, 5);
        assert.strictEqual(state.timeConfig.hoursPerDay, 6);
    });

    test('DiffParser validates changes correctly', () => {
        const parser = new DiffParser();
        const mockResponse = `
<changes>
<change id="1" file="test.js" type="modification">
<description>Valid change</description>
<diff>
--- a/test.js
+++ b/test.js
@@ -1,2 +1,3 @@
 line1
+added line
 line2
</diff>
</change>
<change id="2" file="invalid.js" type="modification">
<description>Invalid change - no diff</description>
<diff></diff>
</change>
</changes>
        `;

        const changes = parser.parseChanges(mockResponse);
        const { valid, invalid } = parser.validateAllChanges(changes);
        
        assert.strictEqual(valid.length, 1);
        assert.strictEqual(invalid.length, 1);
        assert.strictEqual(valid[0].id, '1');
        assert.strictEqual(invalid[0].id, '2');
    });
});
