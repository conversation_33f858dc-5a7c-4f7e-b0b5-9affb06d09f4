"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMService = void 0;
const vscode = __importStar(require("vscode"));
const ErrorHandler_1 = require("../utils/ErrorHandler");
class LLMService {
    static instance;
    errorHandler;
    static getInstance() {
        if (!LLMService.instance) {
            LLMService.instance = new LLMService();
        }
        return LLMService.instance;
    }
    constructor() {
        this.errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
    }
    async isAvailable() {
        try {
            // Check if the Language Model API is available
            const models = await vscode.lm.selectChatModels({
                vendor: 'copilot',
                family: 'gpt-4'
            });
            return models.length > 0;
        }
        catch (error) {
            console.error('LLM API not available:', error);
            return false;
        }
    }
    async generateChanges(projectStructure, fileContents, timeConfig, progressCallback) {
        try {
            if (progressCallback) {
                progressCallback('Initializing AI model...');
            }
            // Select the appropriate model
            const models = await vscode.lm.selectChatModels({
                vendor: 'copilot',
                family: 'gpt-4'
            });
            if (models.length === 0) {
                return {
                    content: '',
                    success: false,
                    error: 'No suitable language model found. Please ensure you have access to GitHub Copilot.'
                };
            }
            const model = models[0];
            if (progressCallback) {
                progressCallback('Generating project analysis prompt...');
            }
            // Calculate required changes
            const totalHours = this.calculateTotalHours(timeConfig);
            const requiredChanges = Math.floor((totalHours * 60) / 1.5); // 90 seconds per change
            // Create the initial prompt
            const initialPrompt = this.createInitialPrompt(projectStructure, requiredChanges, timeConfig);
            if (progressCallback) {
                progressCallback('Sending initial prompt to AI...');
            }
            // Send initial prompt
            const initialRequest = await model.sendRequest([
                vscode.LanguageModelChatMessage.User(initialPrompt)
            ], {}, new vscode.CancellationTokenSource().token);
            let initialResponse = '';
            for await (const fragment of initialRequest.text) {
                initialResponse += fragment;
            }
            if (progressCallback) {
                progressCallback('Processing AI response and preparing file contents...');
            }
            // Parse the response to get requested files
            const requestedFiles = this.parseRequestedFiles(initialResponse);
            // Create follow-up prompt with file contents
            const followUpPrompt = this.createFollowUpPrompt(initialResponse, fileContents, requestedFiles);
            if (progressCallback) {
                progressCallback('Sending file contents to AI...');
            }
            // Send follow-up prompt with file contents
            const followUpRequest = await model.sendRequest([
                vscode.LanguageModelChatMessage.User(initialPrompt),
                vscode.LanguageModelChatMessage.Assistant(initialResponse),
                vscode.LanguageModelChatMessage.User(followUpPrompt)
            ], {}, new vscode.CancellationTokenSource().token);
            let finalResponse = '';
            for await (const fragment of followUpRequest.text) {
                finalResponse += fragment;
            }
            if (progressCallback) {
                progressCallback('AI response received successfully');
            }
            return {
                content: finalResponse,
                success: true
            };
        }
        catch (error) {
            const errorInfo = await this.errorHandler.handleError(error, 'LLM Service');
            return {
                content: '',
                success: false,
                error: this.errorHandler.createUserFriendlyMessage(error, 'generating changes')
            };
        }
    }
    calculateTotalHours(timeConfig) {
        let total = 0;
        for (let day = 1; day <= timeConfig.days; day++) {
            total += timeConfig.customHours[day] || timeConfig.hoursPerDay;
        }
        return total;
    }
    createInitialPrompt(projectStructure, requiredChanges, timeConfig) {
        return `You are an expert software developer tasked with generating retroactive time tracking data. Your goal is to create realistic code changes that will be applied over time to simulate development activity.

<project_structure>
${projectStructure}
</project_structure>

<requirements>
- Generate exactly ${requiredChanges} meaningful code changes
- Changes should be realistic and follow good coding practices
- Each change should be small enough to represent ~90 seconds of work
- Changes should be spread across different files when appropriate
- Include various types of changes: bug fixes, features, refactoring, documentation, etc.
- Ensure changes are compatible with the existing codebase structure
</requirements>

<time_configuration>
- Total days: ${timeConfig.days}
- Hours per day: ${timeConfig.hoursPerDay}
- Custom hours: ${JSON.stringify(timeConfig.customHours)}
</time_configuration>

<instructions>
1. First, analyze the project structure and identify the key files you need to examine
2. Request the content of those files using the format: <request_files>file1.ext,file2.ext,file3.ext</request_files>
3. Do NOT generate any changes yet - just analyze and request files

Please start by analyzing the project structure and requesting the files you need to examine.
</instructions>`;
    }
    parseRequestedFiles(response) {
        const match = response.match(/<request_files>(.*?)<\/request_files>/);
        if (match) {
            return match[1].split(',').map(file => file.trim()).filter(file => file.length > 0);
        }
        return [];
    }
    createFollowUpPrompt(initialResponse, fileContents, requestedFiles) {
        let fileContentsSection = '';
        for (const filePath of requestedFiles) {
            if (fileContents[filePath]) {
                fileContentsSection += `\n<file path="${filePath}">\n${fileContents[filePath]}\n</file>\n`;
            }
        }
        return `Here are the contents of the requested files:

${fileContentsSection}

Now please generate the changes using this exact format:

<changes>
<change id="1" file="path/to/file.ext" type="modification">
<description>Brief description of what this change does</description>
<diff>
--- a/path/to/file.ext
+++ b/path/to/file.ext
@@ -line_number,old_count +line_number,new_count @@
 context line
-removed line
+added line
 context line
</diff>
</change>
<change id="2" file="path/to/file.ext" type="addition">
<description>Brief description of what this change does</description>
<diff>
--- /dev/null
+++ b/path/to/file.ext
@@ -0,0 +1,lines @@
+new file content
+line by line
</diff>
</change>
</changes>

Requirements for the diff format:
- Use standard unified diff format
- Include proper line numbers and counts
- Ensure all changes are valid and can be applied
- Make changes realistic and meaningful
- Distribute changes across multiple files when appropriate
- Each change should represent approximately 90 seconds of development work

Generate all the changes now.`;
    }
}
exports.LLMService = LLMService;
//# sourceMappingURL=LLMService.js.map