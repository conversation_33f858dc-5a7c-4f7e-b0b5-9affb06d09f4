import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';
import { WakatimerOrchestrator } from '../services/WakatimerOrchestrator';

export class ExecutionView {
    private stateManager: StateManager;
    private orchestrator: WakatimerOrchestrator;
    private isPaused: boolean = false;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
        this.orchestrator = new WakatimerOrchestrator(stateManager);
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('Executing Changes', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Applying changes with 90s intervals';
        titleItem.iconPath = new vscode.ThemeIcon('play');
        items.push(titleItem);

        // Progress
        if (state.progress.total > 0) {
            const progressItem = new vscode.TreeItem(
                `Progress: ${state.progress.current}/${state.progress.total}`,
                vscode.TreeItemCollapsibleState.None
            );
            progressItem.description = `${Math.round((state.progress.current / state.progress.total) * 100)}% complete`;
            progressItem.iconPath = new vscode.ThemeIcon('graph');
            items.push(progressItem);
        }

        // Current change
        if (state.progress.stage) {
            const currentItem = new vscode.TreeItem(`Current: ${state.progress.stage}`, vscode.TreeItemCollapsibleState.None);
            currentItem.iconPath = new vscode.ThemeIcon('edit');
            items.push(currentItem);
        }

        // Control buttons
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        if (this.isPaused) {
            const resumeItem = new vscode.TreeItem('▶️ Resume', vscode.TreeItemCollapsibleState.None);
            resumeItem.description = 'Continue execution';
            resumeItem.iconPath = new vscode.ThemeIcon('play');
            resumeItem.command = {
                command: 'wakatimerpro.resumeExecution',
                title: 'Resume Execution'
            };
            items.push(resumeItem);
        } else {
            const pauseItem = new vscode.TreeItem('⏸️ Pause', vscode.TreeItemCollapsibleState.None);
            pauseItem.description = 'Pause execution';
            pauseItem.iconPath = new vscode.ThemeIcon('debug-pause');
            pauseItem.command = {
                command: 'wakatimerpro.pauseExecution',
                title: 'Pause Execution'
            };
            items.push(pauseItem);
        }

        const stopItem = new vscode.TreeItem('⏹️ Stop', vscode.TreeItemCollapsibleState.None);
        stopItem.description = 'Stop execution';
        stopItem.iconPath = new vscode.ThemeIcon('stop');
        stopItem.command = {
            command: 'wakatimerpro.stopExecution',
            title: 'Stop Execution'
        };
        items.push(stopItem);

        // Timeline preview
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        const timelineItem = new vscode.TreeItem('📋 Timeline', vscode.TreeItemCollapsibleState.None);
        timelineItem.description = 'View change timeline';
        timelineItem.iconPath = new vscode.ThemeIcon('list-ordered');
        timelineItem.command = {
            command: 'wakatimerpro.showTimeline',
            title: 'Show Timeline'
        };
        items.push(timelineItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.pauseExecution', () => {
            this.isPaused = true;
            // Pause execution logic here
        });

        vscode.commands.registerCommand('wakatimerpro.resumeExecution', () => {
            this.isPaused = false;
            // Resume execution logic here
        });

        vscode.commands.registerCommand('wakatimerpro.stopExecution', () => {
            this.stateManager.setCurrentStep('complete');
        });

        vscode.commands.registerCommand('wakatimerpro.showTimeline', () => {
            vscode.window.showInformationMessage('Timeline view will be implemented here');
        });
    }
}
