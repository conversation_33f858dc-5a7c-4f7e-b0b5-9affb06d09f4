import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';

export class ExecutionModeView {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];

        // Title
        const titleItem = new vscode.TreeItem('Choose Execution Mode', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Select how to generate changes';
        titleItem.iconPath = new vscode.ThemeIcon('gear');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Simple mode option
        const simpleItem = new vscode.TreeItem('🤖 Simple Mode', vscode.TreeItemCollapsibleState.None);
        simpleItem.description = 'Automated with VSCode AI';
        simpleItem.iconPath = new vscode.ThemeIcon('robot');
        simpleItem.tooltip = 'Uses VSCode\'s built-in Language Model API (GPT-4.1) to automatically generate and apply changes. Fully automated process.';
        simpleItem.command = {
            command: 'wakatimerpro.selectExecutionMode',
            title: 'Select Simple Mode',
            arguments: ['simple']
        };
        items.push(simpleItem);

        // Advanced mode option
        const advancedItem = new vscode.TreeItem('⚙️ Advanced Mode', vscode.TreeItemCollapsibleState.None);
        advancedItem.description = 'Manual with external AI';
        advancedItem.iconPath = new vscode.ThemeIcon('settings');
        advancedItem.tooltip = 'Generates prompts for you to use with external AI tools (Claude, ChatGPT, etc.). You copy the prompt, get the response, and paste it back for processing.';
        advancedItem.command = {
            command: 'wakatimerpro.selectExecutionMode',
            title: 'Select Advanced Mode',
            arguments: ['advanced']
        };
        items.push(advancedItem);

        // Mode descriptions
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const infoItem = new vscode.TreeItem('ℹ️ Mode Information', vscode.TreeItemCollapsibleState.None);
        infoItem.description = 'Click for detailed comparison';
        infoItem.iconPath = new vscode.ThemeIcon('info');
        infoItem.command = {
            command: 'wakatimerpro.showModeInfo',
            title: 'Show Mode Information'
        };
        items.push(infoItem);

        // Back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to time configuration';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['time-config']
        };
        items.push(backItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.selectExecutionMode', (mode: 'simple' | 'advanced') => {
            this.stateManager.setExecutionMode(mode);
            this.stateManager.setCurrentStep('processing');
        });

        vscode.commands.registerCommand('wakatimerpro.showModeInfo', () => {
            const message = `
**Simple Mode:**
• Uses VSCode's built-in Language Model API
• Automatically analyzes your project structure
• Generates appropriate code changes
• Applies changes automatically with 90-second intervals
• Fully automated process
• Requires VSCode with Copilot/Language Model access

**Advanced Mode:**
• Generates detailed prompts for external AI tools
• You copy the prompt to Claude, ChatGPT, or other AI
• Paste the AI response back into the extension
• More control over the AI model and parameters
• Works with any AI tool that supports long prompts
• Manual process but more flexible

Choose Simple Mode for convenience, Advanced Mode for more control or if you don't have VSCode Language Model access.
            `;

            vscode.window.showInformationMessage(message, { modal: true });
        });
    }
}
