import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';
import { WakatimerOrchestrator } from '../services/WakatimerOrchestrator';

export class PreviewView {
    private stateManager: StateManager;
    private orchestrator: WakatimerOrchestrator;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
        this.orchestrator = new WakatimerOrchestrator(stateManager);
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('Changes Ready', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Review before execution';
        titleItem.iconPath = new vscode.ThemeIcon('check');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Status
        const statusItem = new vscode.TreeItem('✅ Changes Verified', vscode.TreeItemCollapsibleState.None);
        statusItem.description = `${state.changes.length} changes ready`;
        statusItem.iconPath = new vscode.ThemeIcon('verified');
        items.push(statusItem);

        // Preview button
        const previewItem = new vscode.TreeItem('👁️ Preview Changes', vscode.TreeItemCollapsibleState.None);
        previewItem.description = 'View diffs in editor';
        previewItem.iconPath = new vscode.ThemeIcon('diff');
        previewItem.command = {
            command: 'wakatimerpro.previewChanges',
            title: 'Preview Changes'
        };
        items.push(previewItem);

        // Summary
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        const summaryItem = new vscode.TreeItem('📊 Summary', vscode.TreeItemCollapsibleState.None);
        summaryItem.description = 'Execution details';
        summaryItem.iconPath = new vscode.ThemeIcon('graph');
        items.push(summaryItem);

        // Start execution button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        const startItem = new vscode.TreeItem('🚀 Start Execution', vscode.TreeItemCollapsibleState.None);
        startItem.description = 'Begin applying changes';
        startItem.iconPath = new vscode.ThemeIcon('play');
        startItem.command = {
            command: 'wakatimerpro.startExecution',
            title: 'Start Execution'
        };
        items.push(startItem);

        // Back button
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to processing';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['processing']
        };
        items.push(backItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.previewChanges', async () => {
            const state = this.stateManager.getState();

            // Create a preview document showing all changes
            const previewContent = this.generatePreviewContent(state.changes);

            const document = await vscode.workspace.openTextDocument({
                content: previewContent,
                language: 'diff'
            });

            await vscode.window.showTextDocument(document, { preview: true });
        });

        vscode.commands.registerCommand('wakatimerpro.startExecution', () => {
            this.orchestrator.executeChanges();
        });
    }

    private generatePreviewContent(changes: any[]): string {
        const lines = [
            '# Wakatimer Pro - Changes Preview',
            '',
            `Total changes: ${changes.length}`,
            '',
            '## Changes to be applied:',
            ''
        ];

        changes.forEach((change, index) => {
            lines.push(`### Change ${index + 1}: ${change.description}`);
            lines.push(`File: ${change.file}`);
            lines.push(`Type: ${change.type}`);
            lines.push('');
            lines.push('```diff');
            lines.push(change.diff);
            lines.push('```');
            lines.push('');
        });

        return lines.join('\n');
    }
}
