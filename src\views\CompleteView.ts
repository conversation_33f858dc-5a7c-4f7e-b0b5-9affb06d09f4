import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';

export class CompleteView {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('✅ Process Complete', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Time tracking data generated successfully';
        titleItem.iconPath = new vscode.ThemeIcon('check-all');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Statistics
        const statsItem = new vscode.TreeItem('📊 Statistics', vscode.TreeItemCollapsibleState.None);
        statsItem.description = 'View execution summary';
        statsItem.iconPath = new vscode.ThemeIcon('graph');
        items.push(statsItem);

        const changesItem = new vscode.TreeItem(`Changes Applied: ${state.changes.length}`, vscode.TreeItemCollapsibleState.None);
        changesItem.iconPath = new vscode.ThemeIcon('edit');
        items.push(changesItem);

        const timeItem = new vscode.TreeItem('Time Simulated: X hours', vscode.TreeItemCollapsibleState.None);
        timeItem.iconPath = new vscode.ThemeIcon('clock');
        items.push(timeItem);

        // Actions
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        const newSessionItem = new vscode.TreeItem('🔄 Start New Session', vscode.TreeItemCollapsibleState.None);
        newSessionItem.description = 'Begin another time tracking session';
        newSessionItem.iconPath = new vscode.ThemeIcon('refresh');
        newSessionItem.command = {
            command: 'wakatimerpro.startNewSession',
            title: 'Start New Session'
        };
        items.push(newSessionItem);

        const exportItem = new vscode.TreeItem('💾 Export Report', vscode.TreeItemCollapsibleState.None);
        exportItem.description = 'Save execution report';
        exportItem.iconPath = new vscode.ThemeIcon('save');
        exportItem.command = {
            command: 'wakatimerpro.exportReport',
            title: 'Export Report'
        };
        items.push(exportItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.startNewSession', () => {
            this.stateManager.resetState();
        });

        vscode.commands.registerCommand('wakatimerpro.exportReport', () => {
            vscode.window.showInformationMessage('Export functionality will be implemented here');
        });
    }
}
