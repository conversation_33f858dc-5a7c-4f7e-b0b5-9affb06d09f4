{"name": "wa<PERSON><PERSON><PERSON><PERSON>", "displayName": "Wakatimer Pro", "description": "Professional retroactive time tracking data generator for VSCode", "version": "1.0.0", "publisher": "wa<PERSON><PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.85.0"}, "categories": ["Other", "Productivity"], "keywords": ["time tracking", "wakatime", "productivity", "retroactive", "automation"], "activationEvents": ["onView:wakatimerpro.sidePanel"], "main": "./out/extension.js", "contributes": {"views": {"explorer": [{"id": "wakatimerpro.sidePanel", "name": "Wakatimer Pro", "when": "true"}]}, "commands": [{"command": "wakatimerpro.openFolderDialog", "title": "Open Folder", "category": "Wakatimer Pro"}, {"command": "wakatimerpro.refresh", "title": "Refresh", "category": "Wakatimer Pro"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test"}, "devDependencies": {"@types/node": "^24.0.10", "@types/vscode": "^1.101.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.6", "eslint": "^8.57.1", "typescript": "^5.8.3"}, "author": "Wakatimer Pro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wakatimerpro/wakatimerpro.git"}, "bugs": {"url": "https://github.com/wakatimerpro/wakatimerpro/issues"}, "homepage": "https://github.com/wakatimerpro/wakatimerpro#readme"}