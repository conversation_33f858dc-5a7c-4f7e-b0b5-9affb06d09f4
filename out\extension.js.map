{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,4BAeC;AA+SD,gCAEC;AApUD,+CAAiC;AACjC,2EAAwE;AACxE,sDAAmD;AAEnD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,2BAA2B;IAC3B,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;IAE/C,8CAA8C;IAC9C,MAAM,QAAQ,GAAG,IAAI,2CAAoB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACjE,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;IAE3E,sDAAsD;IACtD,mBAAmB,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAErD,0BAA0B;IAC1B,QAAQ,CAAC,OAAO,EAAE,CAAC;AACvB,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAgC,EAAE,YAA0B,EAAE,QAA8B;IACrH,wDAAwD;IACxD,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE7C,8CAA8C;IAC9C,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAE,QAAiC,EAAE,EAAE;QACjF,IAAI,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,kDAAkD,CAAC,CAAC;YACpF,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,2CAA2C;YAC3C,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC9C,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/B,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,yCAAyC,CAAC,CAAC;oBAC3E,OAAO;gBACX,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YACxE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,oCAAoC,SAAS,EAAE,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,8BAA8B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACL,CAAC,CAAC;IAEF,gBAAgB;IAChB,mBAAmB,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC5D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YACjD,gBAAgB,EAAE,IAAI;YACtB,cAAc,EAAE,KAAK;YACrB,aAAa,EAAE,KAAK;YACpB,SAAS,EAAE,eAAe;SAC7B,CAAC,CAAC;QAEH,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,sBAAsB,EAAE,GAAG,EAAE;QAC7C,QAAQ,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,mBAAmB,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC3C,YAAY,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,yBAAyB,EAAE,CAAC,IAAuC,EAAE,EAAE;QACvF,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3B,YAAY,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,qBAAqB,EAAE,CAAC,IAAY,EAAE,EAAE;QACxD,YAAY,CAAC,cAAc,CAAC,IAAW,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,mCAAmC,EAAE,CAAC,aAAqB,EAAE,EAAE;QAC/E,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAEtC,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACjC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACjD,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACJ,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACjD,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,mBAAmB,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACzD,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,6BAA6B;YACrC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;oBACpC,OAAO,wCAAwC,CAAC;gBACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,YAAY,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;QAC1D,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,4BAA4B;YACpC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;oBACpC,OAAO,wCAAwC,CAAC;gBACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpC,YAAY,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC3D,YAAY,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACxD,oDAAoD;QACpD,QAAQ,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,mCAAmC,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;QAC3E,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC;QAEvF,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,uBAAuB,GAAG,SAAS;YAC3C,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE;YAC9B,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;oBACpC,OAAO,wCAAwC,CAAC;gBACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACxD,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzB,YAAY,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,mBAAmB,CAAC,kCAAkC,EAAE,CAAC,IAA2B,EAAE,EAAE;QACpF,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACpC,YAAY,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,2BAA2B,EAAE,GAAG,EAAE;QAClD,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;sHAgB8F,CAAC;QAE/G,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,mBAAmB,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACrD,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,8DAA8D;IAClE,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,gCAAgC,EAAE,GAAG,EAAE;QACvD,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,gCAAgC,EAAE,GAAG,EAAE;QACvD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACvB,MAAM,EAAE,4BAA4B;YACpC,WAAW,EAAE,kDAAkD;YAC/D,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACf,IAAI,QAAQ,EAAE,CAAC;gBACX,8EAA8E;gBAC9E,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;YAClF,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,mBAAmB,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;QAC1D,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAEtC,gDAAgD;QAChD,MAAM,cAAc,GAAG,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE7D,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACrD,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACpD,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,mBAAmB,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACpD,kDAAkD;IACtD,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACrD,mDAAmD;IACvD,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACnD,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAEtC,uBAAuB;QACvB,MAAM,eAAe,GAAG,uBAAuB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE/E,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACrD,OAAO,EAAE,eAAe;YACxB,QAAQ,EAAE,UAAU;SACvB,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,mBAAmB,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACrD,YAAY,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,mBAAmB,CAAC,2BAA2B,EAAE,GAAG,EAAE;QAClD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+CAA+C,CAAC,CAAC;IAC1F,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAc;IAC1C,MAAM,KAAK,GAAG;QACV,mCAAmC;QACnC,EAAE;QACF,kBAAkB,OAAO,CAAC,MAAM,EAAE;QAClC,EAAE;QACF,2BAA2B;QAC3B,EAAE;KACL,CAAC;IAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAC9B,KAAK,CAAC,IAAI,CAAC,cAAc,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC,WAAW,IAAI,gBAAgB,EAAE,CAAC,CAAC;QACjF,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,cAAc,EAAE,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,cAAc,EAAE,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,mBAAmB,CAAC,CAAC;QAC/C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,uBAAuB,CAAC,OAAc,EAAE,QAAa;IAC1D,MAAM,KAAK,GAAG;QACV,sCAAsC;QACtC,EAAE;QACF,aAAa,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,kBAAkB;QACjE,kBAAkB,QAAQ,CAAC,KAAK,EAAE;QAClC,EAAE;QACF,cAAc;QACd,EAAE;KACL,CAAC;IAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAC9B,MAAM,MAAM,GAAG,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;QACxF,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,WAAW,IAAI,gBAAgB,EAAE,CAAC,CAAC;QAC3F,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,IAAI,IAAI,cAAc,IAAI,CAAC,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,IAAI,cAAc,EAAE,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;AAC/D,CAAC"}