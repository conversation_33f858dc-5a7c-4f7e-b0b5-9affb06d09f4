"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectAnalyzer = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class ProjectAnalyzer {
    static IGNORED_DIRS = [
        'node_modules', '.git', '.vscode', 'dist', 'out', 'build',
        '.vscode-test', 'coverage', '.nyc_output', 'target', 'bin', 'obj'
    ];
    static IGNORED_FILES = [
        '.gitignore', '.vscodeignore', 'package-lock.json', 'yarn.lock',
        '.DS_Store', 'Thumbs.db', '*.log', '*.tmp'
    ];
    async analyzeProject(rootPath) {
        const files = [];
        const directories = [];
        await this.scanDirectory(rootPath, rootPath, files, directories);
        const tree = await this.generateTree(rootPath);
        const summary = this.generateSummary(files);
        return {
            files,
            directories,
            tree,
            summary
        };
    }
    async scanDirectory(currentPath, rootPath, files, directories) {
        try {
            const items = await fs.promises.readdir(currentPath, { withFileTypes: true });
            for (const item of items) {
                const fullPath = path.join(currentPath, item.name);
                const relativePath = path.relative(rootPath, fullPath);
                if (item.isDirectory()) {
                    if (!this.shouldIgnoreDirectory(item.name)) {
                        directories.push(relativePath);
                        await this.scanDirectory(fullPath, rootPath, files, directories);
                    }
                }
                else {
                    if (!this.shouldIgnoreFile(item.name)) {
                        files.push(relativePath);
                    }
                }
            }
        }
        catch (error) {
            console.error(`Error scanning directory ${currentPath}:`, error);
        }
    }
    shouldIgnoreDirectory(dirName) {
        return ProjectAnalyzer.IGNORED_DIRS.includes(dirName) || dirName.startsWith('.');
    }
    shouldIgnoreFile(fileName) {
        if (fileName.startsWith('.') && fileName !== '.env') {
            return true;
        }
        return ProjectAnalyzer.IGNORED_FILES.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(fileName);
            }
            return fileName === pattern;
        });
    }
    async generateTree(rootPath, maxDepth = 3) {
        const tree = [];
        await this.buildTree(rootPath, rootPath, '', 0, maxDepth, tree);
        return tree.join('\n');
    }
    async buildTree(currentPath, rootPath, prefix, depth, maxDepth, tree) {
        if (depth > maxDepth) {
            return;
        }
        try {
            const items = await fs.promises.readdir(currentPath, { withFileTypes: true });
            const filteredItems = items.filter(item => {
                if (item.isDirectory()) {
                    return !this.shouldIgnoreDirectory(item.name);
                }
                else {
                    return !this.shouldIgnoreFile(item.name);
                }
            });
            filteredItems.sort((a, b) => {
                // Directories first, then files
                if (a.isDirectory() && !b.isDirectory())
                    return -1;
                if (!a.isDirectory() && b.isDirectory())
                    return 1;
                return a.name.localeCompare(b.name);
            });
            for (let i = 0; i < filteredItems.length; i++) {
                const item = filteredItems[i];
                const isLast = i === filteredItems.length - 1;
                const currentPrefix = isLast ? '└── ' : '├── ';
                const nextPrefix = isLast ? '    ' : '│   ';
                tree.push(`${prefix}${currentPrefix}${item.name}`);
                if (item.isDirectory()) {
                    const fullPath = path.join(currentPath, item.name);
                    await this.buildTree(fullPath, rootPath, prefix + nextPrefix, depth + 1, maxDepth, tree);
                }
            }
        }
        catch (error) {
            console.error(`Error building tree for ${currentPath}:`, error);
        }
    }
    generateSummary(files) {
        const fileTypes = {};
        const languages = new Set();
        for (const file of files) {
            const ext = path.extname(file).toLowerCase();
            fileTypes[ext] = (fileTypes[ext] || 0) + 1;
            // Map extensions to languages
            const language = this.getLanguageFromExtension(ext);
            if (language) {
                languages.add(language);
            }
        }
        return {
            totalFiles: files.length,
            fileTypes,
            languages: Array.from(languages)
        };
    }
    getLanguageFromExtension(ext) {
        const languageMap = {
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React',
            '.tsx': 'React TypeScript',
            '.py': 'Python',
            '.java': 'Java',
            '.c': 'C',
            '.cpp': 'C++',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.less': 'Less',
            '.json': 'JSON',
            '.xml': 'XML',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.md': 'Markdown',
            '.sql': 'SQL',
            '.sh': 'Shell',
            '.ps1': 'PowerShell',
            '.dockerfile': 'Docker',
            '.vue': 'Vue',
            '.svelte': 'Svelte'
        };
        return languageMap[ext] || null;
    }
    async getFileContent(filePath) {
        try {
            return await fs.promises.readFile(filePath, 'utf-8');
        }
        catch (error) {
            console.error(`Error reading file ${filePath}:`, error);
            return '';
        }
    }
    async getMultipleFileContents(rootPath, relativePaths) {
        const contents = {};
        for (const relativePath of relativePaths) {
            const fullPath = path.join(rootPath, relativePath);
            try {
                const content = await this.getFileContent(fullPath);
                contents[relativePath] = content;
            }
            catch (error) {
                console.error(`Error reading file ${relativePath}:`, error);
                contents[relativePath] = `// Error reading file: ${error}`;
            }
        }
        return contents;
    }
}
exports.ProjectAnalyzer = ProjectAnalyzer;
//# sourceMappingURL=ProjectAnalyzer.js.map