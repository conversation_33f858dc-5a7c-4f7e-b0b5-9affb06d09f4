"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiffParser = void 0;
class DiffParser {
    parseChanges(llmResponse) {
        const changes = [];
        // Extract changes from the LLM response
        const changesMatch = llmResponse.match(/<changes>(.*?)<\/changes>/s);
        if (!changesMatch) {
            throw new Error('No changes found in LLM response. Expected <changes>...</changes> tags.');
        }
        const changesContent = changesMatch[1];
        // Parse individual changes
        const changeMatches = changesContent.matchAll(/<change\s+id="([^"]+)"\s+file="([^"]+)"\s+type="([^"]+)">(.*?)<\/change>/gs);
        for (const match of changeMatches) {
            const [, id, file, type, content] = match;
            // Extract description
            const descMatch = content.match(/<description>(.*?)<\/description>/s);
            const description = descMatch ? descMatch[1].trim() : '';
            // Extract diff
            const diffMatch = content.match(/<diff>(.*?)<\/diff>/s);
            const diff = diffMatch ? diffMatch[1].trim() : '';
            if (!diff) {
                changes.push({
                    id,
                    file,
                    type: type,
                    description,
                    diff: '',
                    hunks: [],
                    isValid: false,
                    error: 'No diff content found'
                });
                continue;
            }
            try {
                const hunks = this.parseDiff(diff);
                const isValid = this.validateChange(file, type, hunks);
                changes.push({
                    id,
                    file,
                    type: type,
                    description,
                    diff,
                    hunks,
                    isValid,
                    error: isValid ? undefined : 'Invalid diff format or content'
                });
            }
            catch (error) {
                changes.push({
                    id,
                    file,
                    type: type,
                    description,
                    diff,
                    hunks: [],
                    isValid: false,
                    error: `Parse error: ${error}`
                });
            }
        }
        return changes;
    }
    parseDiff(diffContent) {
        const lines = diffContent.split('\n');
        const hunks = [];
        let currentHunk = null;
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            // Skip file headers
            if (line.startsWith('---') || line.startsWith('+++')) {
                continue;
            }
            // Parse hunk header
            const hunkMatch = line.match(/^@@\s+-(\d+)(?:,(\d+))?\s+\+(\d+)(?:,(\d+))?\s+@@/);
            if (hunkMatch) {
                if (currentHunk) {
                    hunks.push(currentHunk);
                }
                const oldStart = parseInt(hunkMatch[1]);
                const oldCount = hunkMatch[2] ? parseInt(hunkMatch[2]) : 1;
                const newStart = parseInt(hunkMatch[3]);
                const newCount = hunkMatch[4] ? parseInt(hunkMatch[4]) : 1;
                currentHunk = {
                    oldStart,
                    oldCount,
                    newStart,
                    newCount,
                    lines: []
                };
                continue;
            }
            // Parse diff lines
            if (currentHunk && line.length > 0) {
                const firstChar = line[0];
                const content = line.slice(1);
                let type;
                switch (firstChar) {
                    case '+':
                        type = 'addition';
                        break;
                    case '-':
                        type = 'deletion';
                        break;
                    case ' ':
                        type = 'context';
                        break;
                    default:
                        // Skip invalid lines
                        continue;
                }
                currentHunk.lines.push({
                    type,
                    content,
                });
            }
        }
        if (currentHunk) {
            hunks.push(currentHunk);
        }
        return hunks;
    }
    validateChange(file, type, hunks) {
        // Basic validation
        if (!file || !type) {
            return false;
        }
        if (type === 'addition') {
            // For new files, we expect hunks starting from line 1
            return hunks.length > 0 && hunks[0].oldStart === 0;
        }
        if (type === 'deletion') {
            // For deletions, we expect only deletion lines
            return hunks.every(hunk => hunk.lines.every(line => line.type === 'deletion' || line.type === 'context'));
        }
        if (type === 'modification') {
            // For modifications, we expect at least one addition or deletion
            return hunks.some(hunk => hunk.lines.some(line => line.type === 'addition' || line.type === 'deletion'));
        }
        return false;
    }
    validateAllChanges(changes) {
        const valid = changes.filter(change => change.isValid);
        const invalid = changes.filter(change => !change.isValid);
        return { valid, invalid };
    }
    generateSummary(changes) {
        const { valid, invalid } = this.validateAllChanges(changes);
        const summary = [
            `Total changes: ${changes.length}`,
            `Valid changes: ${valid.length}`,
            `Invalid changes: ${invalid.length}`,
            '',
            'Changes by type:',
            `  Modifications: ${valid.filter(c => c.type === 'modification').length}`,
            `  Additions: ${valid.filter(c => c.type === 'addition').length}`,
            `  Deletions: ${valid.filter(c => c.type === 'deletion').length}`,
        ];
        if (invalid.length > 0) {
            summary.push('', 'Invalid changes:');
            invalid.forEach(change => {
                summary.push(`  ${change.id}: ${change.error}`);
            });
        }
        return summary.join('\n');
    }
}
exports.DiffParser = DiffParser;
//# sourceMappingURL=DiffParser.js.map