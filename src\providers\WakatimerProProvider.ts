import * as vscode from 'vscode';
import { StateManager, AppState } from '../core/StateManager';
import { WelcomeView } from '../views/WelcomeView';
import { ModeSelectionView } from '../views/ModeSelectionView';
import { DirectorySelectionView } from '../views/DirectorySelectionView';
import { FileCopyView } from '../views/FileCopyView';
import { TimeConfigView } from '../views/TimeConfigView';
import { ExecutionModeView } from '../views/ExecutionModeView';
import { ProcessingView } from '../views/ProcessingView';
import { PreviewView } from '../views/PreviewView';
import { ExecutionView } from '../views/ExecutionView';
import { CompleteView } from '../views/CompleteView';

export class WakatimerProProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | undefined | null | void> = new vscode.EventEmitter<vscode.TreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private context: vscode.ExtensionContext;
    private stateManager: StateManager;
    private currentView: any;

    constructor(context: vscode.ExtensionContext, stateManager: StateManager) {
        this.context = context;
        this.stateManager = stateManager;
        
        // Listen to state changes
        this.stateManager.onStateChange((state) => {
            this.updateCurrentView(state);
            this.refresh();
        });

        this.updateCurrentView(this.stateManager.getState());
    }

    private updateCurrentView(state: AppState): void {
        switch (state.currentStep) {
            case 'welcome':
                this.currentView = new WelcomeView(this.stateManager);
                break;
            case 'mode-selection':
                this.currentView = new ModeSelectionView(this.stateManager);
                break;
            case 'directory-selection':
                this.currentView = new DirectorySelectionView(this.stateManager);
                break;
            case 'file-copy':
                this.currentView = new FileCopyView(this.stateManager);
                break;
            case 'time-config':
                this.currentView = new TimeConfigView(this.stateManager);
                break;
            case 'execution-mode':
                this.currentView = new ExecutionModeView(this.stateManager);
                break;
            case 'processing':
                this.currentView = new ProcessingView(this.stateManager);
                break;
            case 'preview':
                this.currentView = new PreviewView(this.stateManager);
                break;
            case 'execution':
                this.currentView = new ExecutionView(this.stateManager);
                break;
            case 'complete':
                this.currentView = new CompleteView(this.stateManager);
                break;
            default:
                this.currentView = new WelcomeView(this.stateManager);
        }
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {
        if (!element) {
            // Return root items from current view
            return this.currentView ? await this.currentView.getItems() : [];
        }
        
        // Handle child items if needed
        return [];
    }

    async handleFolderSelection(folderUri: vscode.Uri): Promise<void> {
        if (this.currentView && this.currentView.handleFolderSelection) {
            await this.currentView.handleFolderSelection(folderUri);
        }
    }
}
