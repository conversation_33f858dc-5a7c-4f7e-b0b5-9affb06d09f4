"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WakatimerOrchestrator = void 0;
const LLMService_1 = require("./LLMService");
const ProjectAnalyzer_1 = require("./ProjectAnalyzer");
const DiffParser_1 = require("./DiffParser");
const ChangeApplicator_1 = require("./ChangeApplicator");
class WakatimerOrchestrator {
    stateManager;
    llmService;
    projectAnalyzer;
    diffParser;
    changeApplicator;
    isExecuting = false;
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.llmService = LLMService_1.LLMService.getInstance();
        this.projectAnalyzer = new ProjectAnalyzer_1.ProjectAnalyzer();
        this.diffParser = new DiffParser_1.DiffParser();
        this.changeApplicator = new ChangeApplicator_1.ChangeApplicator();
    }
    async executeSimpleMode() {
        // Prevent multiple simultaneous executions
        if (this.isExecuting) {
            console.log('Simple mode execution already in progress, skipping...');
            return;
        }
        this.isExecuting = true;
        const state = this.stateManager.getState();
        try {
            this.stateManager.setProcessing(true);
            this.stateManager.setError(null);
            // Step 1: Check LLM availability
            this.stateManager.setProgress({ current: 1, total: 6, stage: 'Checking AI model availability...' });
            const isLLMAvailable = await this.llmService.isAvailable();
            if (!isLLMAvailable) {
                throw new Error('VSCode Language Model API is not available. Please ensure you have access to GitHub Copilot.');
            }
            // Step 2: Analyze project structure
            this.stateManager.setProgress({ current: 2, total: 6, stage: 'Analyzing project structure...' });
            const workingDirectory = state.destinationDirectory || state.sourceDirectory;
            if (!workingDirectory) {
                throw new Error('No working directory specified');
            }
            const projectStructure = await this.projectAnalyzer.analyzeProject(workingDirectory);
            // Step 3: Generate changes with AI
            this.stateManager.setProgress({ current: 3, total: 6, stage: 'Generating changes with AI...' });
            const llmResponse = await this.llmService.generateChanges(projectStructure.tree, {}, // Will be populated by LLM service based on requested files
            state.timeConfig, (stage) => {
                this.stateManager.setProgress({ stage });
            });
            if (!llmResponse.success) {
                throw new Error(llmResponse.error || 'Failed to generate changes');
            }
            // Step 4: Parse and validate changes
            this.stateManager.setProgress({ current: 4, total: 6, stage: 'Parsing and validating changes...' });
            const changes = this.diffParser.parseChanges(llmResponse.content);
            const { valid, invalid } = this.diffParser.validateAllChanges(changes);
            if (valid.length === 0) {
                throw new Error('No valid changes were generated');
            }
            if (invalid.length > 0) {
                console.warn(`${invalid.length} invalid changes were filtered out`);
            }
            // Step 5: Store changes and move to preview
            this.stateManager.setProgress({ current: 5, total: 6, stage: 'Preparing changes for preview...' });
            this.stateManager.setChanges(valid);
            // Step 6: Complete processing
            this.stateManager.setProgress({ current: 6, total: 6, stage: 'Processing complete' });
            this.stateManager.setProcessing(false);
            this.stateManager.setCurrentStep('preview');
        }
        catch (error) {
            console.error('Error in simple mode execution:', error);
            this.stateManager.setError(error instanceof Error ? error.message : String(error));
            this.stateManager.setProcessing(false);
        }
        finally {
            this.isExecuting = false;
        }
    }
    async executeAdvancedMode() {
        // Prevent multiple simultaneous executions
        if (this.isExecuting) {
            console.log('Advanced mode execution already in progress, skipping...');
            throw new Error('Execution already in progress');
        }
        this.isExecuting = true;
        const state = this.stateManager.getState();
        try {
            this.stateManager.setProcessing(true);
            this.stateManager.setError(null);
            // Step 1: Analyze project structure
            this.stateManager.setProgress({ current: 1, total: 3, stage: 'Analyzing project structure...' });
            const workingDirectory = state.destinationDirectory || state.sourceDirectory;
            if (!workingDirectory) {
                throw new Error('No working directory specified');
            }
            const projectStructure = await this.projectAnalyzer.analyzeProject(workingDirectory);
            // Step 2: Generate prompt
            this.stateManager.setProgress({ current: 2, total: 3, stage: 'Generating prompt for external AI...' });
            const prompt = this.generateAdvancedModePrompt(projectStructure, state.timeConfig);
            // Step 3: Complete
            this.stateManager.setProgress({ current: 3, total: 3, stage: 'Prompt generated successfully' });
            this.stateManager.setProcessing(false);
            return prompt;
        }
        catch (error) {
            console.error('Error in advanced mode execution:', error);
            this.stateManager.setError(error instanceof Error ? error.message : String(error));
            this.stateManager.setProcessing(false);
            throw error;
        }
        finally {
            this.isExecuting = false;
        }
    }
    async processAdvancedModeResponse(response) {
        try {
            this.stateManager.setProcessing(true);
            this.stateManager.setError(null);
            // Parse and validate the response
            this.stateManager.setProgress({ current: 1, total: 2, stage: 'Parsing AI response...' });
            const changes = this.diffParser.parseChanges(response);
            const { valid, invalid } = this.diffParser.validateAllChanges(changes);
            if (valid.length === 0) {
                throw new Error('No valid changes found in the response');
            }
            if (invalid.length > 0) {
                console.warn(`${invalid.length} invalid changes were filtered out`);
            }
            // Store changes and move to preview
            this.stateManager.setProgress({ current: 2, total: 2, stage: 'Changes processed successfully' });
            this.stateManager.setChanges(valid);
            this.stateManager.setProcessing(false);
            this.stateManager.setCurrentStep('preview');
        }
        catch (error) {
            console.error('Error processing advanced mode response:', error);
            this.stateManager.setError(error instanceof Error ? error.message : String(error));
            this.stateManager.setProcessing(false);
        }
    }
    async executeChanges() {
        const state = this.stateManager.getState();
        try {
            this.stateManager.setCurrentStep('execution');
            const workingDirectory = state.destinationDirectory || state.sourceDirectory;
            if (!workingDirectory) {
                throw new Error('No working directory specified');
            }
            const result = await this.changeApplicator.applyChanges(state.changes, workingDirectory, (current, total, stage) => {
                this.stateManager.setProgress({ current, total, stage });
            }, () => this.changeApplicator.isPausedState());
            if (result.success) {
                this.stateManager.setCurrentStep('complete');
            }
            else {
                this.stateManager.setError(result.error || 'Failed to apply all changes');
            }
        }
        catch (error) {
            console.error('Error executing changes:', error);
            this.stateManager.setError(error instanceof Error ? error.message : String(error));
        }
    }
    pauseExecution() {
        this.changeApplicator.pause();
    }
    resumeExecution() {
        this.changeApplicator.resume();
    }
    stopExecution() {
        this.changeApplicator.stop();
        this.stateManager.setCurrentStep('complete');
    }
    generateAdvancedModePrompt(projectStructure, timeConfig) {
        const totalHours = this.calculateTotalHours(timeConfig);
        const requiredChanges = Math.floor((totalHours * 60) / 1.5);
        return `You are an expert software developer tasked with generating retroactive time tracking data. Your goal is to create realistic code changes that will be applied over time to simulate development activity.

<project_structure>
${projectStructure.tree}
</project_structure>

<project_summary>
Total files: ${projectStructure.summary.totalFiles}
Languages: ${projectStructure.summary.languages.join(', ')}
File types: ${Object.entries(projectStructure.summary.fileTypes).map(([ext, count]) => `${ext}: ${count}`).join(', ')}
</project_summary>

<requirements>
- Generate exactly ${requiredChanges} meaningful code changes
- Changes should be realistic and follow good coding practices
- Each change should be small enough to represent ~90 seconds of work
- Changes should be spread across different files when appropriate
- Include various types of changes: bug fixes, features, refactoring, documentation, etc.
- Ensure changes are compatible with the existing codebase structure
</requirements>

<time_configuration>
- Total days: ${timeConfig.days}
- Hours per day: ${timeConfig.hoursPerDay}
- Custom hours: ${JSON.stringify(timeConfig.customHours)}
</time_configuration>

Please generate the changes using this exact format:

<changes>
<change id="1" file="path/to/file.ext" type="modification">
<description>Brief description of what this change does</description>
<diff>
--- a/path/to/file.ext
+++ b/path/to/file.ext
@@ -line_number,old_count +line_number,new_count @@
 context line
-removed line
+added line
 context line
</diff>
</change>
</changes>

Requirements for the diff format:
- Use standard unified diff format
- Include proper line numbers and counts
- Ensure all changes are valid and can be applied
- Make changes realistic and meaningful
- Distribute changes across multiple files when appropriate
- Each change should represent approximately 90 seconds of development work`;
    }
    calculateTotalHours(timeConfig) {
        let total = 0;
        for (let day = 1; day <= timeConfig.days; day++) {
            total += timeConfig.customHours[day] || timeConfig.hoursPerDay;
        }
        return total;
    }
}
exports.WakatimerOrchestrator = WakatimerOrchestrator;
//# sourceMappingURL=WakatimerOrchestrator.js.map