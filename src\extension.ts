import * as vscode from 'vscode';
import { WakatimerProProvider } from './providers/WakatimerProProvider';
import { StateManager } from './core/StateManager';

export function activate(context: vscode.ExtensionContext) {
    console.log('Wakatimer Pro extension is now active!');

    // Initialize state manager
    const stateManager = new StateManager(context);

    // Create and register the side panel provider
    const provider = new WakatimerProProvider(context, stateManager);
    vscode.window.registerTreeDataProvider('wakatimerpro.sidePanel', provider);

    // Register commands
    const openFolderCommand = vscode.commands.registerCommand('wakatimerpro.openFolderDialog', async () => {
        const folderUri = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Folder'
        });

        if (folderUri && folderUri[0]) {
            await provider.handleFolderSelection(folderUri[0]);
        }
    });

    const refreshCommand = vscode.commands.registerCommand('wakatimerpro.refresh', () => {
        provider.refresh();
    });

    // Add commands to context subscriptions
    context.subscriptions.push(openFolderCommand, refreshCommand);

    // Initialize the provider
    provider.refresh();
}

export function deactivate() {
    console.log('Wakatimer Pro extension is now deactivated!');
}
