import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';

export class ModeSelectionView {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];

        // Title
        const titleItem = new vscode.TreeItem('Choose Working Mode', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Select how you want to work with your files';
        titleItem.iconPath = new vscode.ThemeIcon('gear');
        items.push(titleItem);

        // Spacer
        const spacerItem = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        items.push(spacerItem);

        // Edit in place option
        const editInPlaceItem = new vscode.TreeItem('📝 Edit in Place', vscode.TreeItemCollapsibleState.None);
        editInPlaceItem.description = 'Modify files in current workspace';
        editInPlaceItem.iconPath = new vscode.ThemeIcon('edit');
        editInPlaceItem.tooltip = 'Work directly with files in your current workspace. Changes will be applied to existing files.';
        editInPlaceItem.command = {
            command: 'wakatimerpro.selectMode',
            title: 'Select Edit in Place',
            arguments: ['edit-in-place']
        };
        items.push(editInPlaceItem);

        // Copy and edit option
        const copyAndEditItem = new vscode.TreeItem('📁 Make a Copy and Edit', vscode.TreeItemCollapsibleState.None);
        copyAndEditItem.description = 'Copy files to new location first';
        copyAndEditItem.iconPath = new vscode.ThemeIcon('copy');
        copyAndEditItem.tooltip = 'Copy files from a source directory to a destination directory, then apply changes to the copies.';
        copyAndEditItem.command = {
            command: 'wakatimerpro.selectMode',
            title: 'Select Copy and Edit',
            arguments: ['copy-and-edit']
        };
        items.push(copyAndEditItem);

        // Back button
        const spacer2Item = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        items.push(spacer2Item);

        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to welcome screen';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['welcome']
        };
        items.push(backItem);

        return items;
    }
}
