"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WelcomeView = void 0;
const vscode = __importStar(require("vscode"));
class WelcomeView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        // Extension title
        const titleItem = new vscode.TreeItem('Wakatimer Pro', vscode.TreeItemCollapsibleState.None);
        titleItem.description = '';
        titleItem.iconPath = new vscode.ThemeIcon('clock');
        titleItem.tooltip = 'Professional retroactive time tracking data generator';
        items.push(titleItem);
        // Version info
        const versionItem = new vscode.TreeItem('Version 1.0.0', vscode.TreeItemCollapsibleState.None);
        versionItem.description = '';
        versionItem.iconPath = new vscode.ThemeIcon('info');
        versionItem.tooltip = 'Current extension version';
        items.push(versionItem);
        // Spacer
        const spacerItem = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        spacerItem.description = '';
        items.push(spacerItem);
        // Start button
        const startItem = new vscode.TreeItem('🚀 Start', vscode.TreeItemCollapsibleState.None);
        startItem.description = 'Begin time tracking setup';
        startItem.iconPath = new vscode.ThemeIcon('play');
        startItem.tooltip = 'Click to start the time tracking setup process';
        startItem.command = {
            command: 'wakatimerpro.start',
            title: 'Start',
            arguments: []
        };
        items.push(startItem);
        // Register the start command if not already registered
        this.registerStartCommand();
        return items;
    }
    registerStartCommand() {
        // Register the start command
        vscode.commands.registerCommand('wakatimerpro.start', () => {
            this.stateManager.setCurrentStep('mode-selection');
        });
    }
}
exports.WelcomeView = WelcomeView;
//# sourceMappingURL=WelcomeView.js.map