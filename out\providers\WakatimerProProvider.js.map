{"version": 3, "file": "WakatimerProProvider.js", "sourceRoot": "", "sources": ["../../src/providers/WakatimerProProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,sDAAmD;AACnD,kEAA+D;AAC/D,4EAAyE;AACzE,wDAAqD;AACrD,4DAAyD;AACzD,kEAA+D;AAC/D,4DAAyD;AACzD,sDAAmD;AACnD,0DAAuD;AACvD,wDAAqD;AAErD,MAAa,oBAAoB;IACrB,oBAAoB,GAAmE,IAAI,MAAM,CAAC,YAAY,EAA6C,CAAC;IAC3J,mBAAmB,GAA4D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEhH,OAAO,CAA0B;IACjC,YAAY,CAAe;IAC3B,WAAW,CAAM;IAEzB,YAAY,OAAgC,EAAE,YAA0B;QACpE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,0BAA0B;QAC1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,EAAE;YACtC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzD,CAAC;IAEO,iBAAiB,CAAC,KAAe;QACrC,QAAQ,KAAK,CAAC,WAAW,EAAE,CAAC;YACxB,KAAK,SAAS;gBACV,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACtD,MAAM;YACV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,WAAW,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5D,MAAM;YACV,KAAK,qBAAqB;gBACtB,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjE,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,WAAW,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACvD,MAAM;YACV,KAAK,aAAa;gBACd,IAAI,CAAC,WAAW,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,WAAW,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5D,MAAM;YACV,KAAK,YAAY;gBACb,IAAI,CAAC,WAAW,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACtD,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,WAAW,GAAG,IAAI,6BAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxD,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,WAAW,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACvD,MAAM;YACV;gBACI,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAwB;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAyB;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,sCAAsC;YACtC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE,CAAC;QAED,+BAA+B;QAC/B,OAAO,EAAE,CAAC;IACd,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,SAAqB;QAC7C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;CACJ;AAjFD,oDAiFC"}