import * as vscode from 'vscode';

export enum ErrorSeverity {
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error',
    CRITICAL = 'critical'
}

export interface ErrorInfo {
    message: string;
    severity: ErrorSeverity;
    code?: string;
    details?: string;
    canRetry?: boolean;
    canContinue?: boolean;
}

export class ErrorHandler {
    private static instance: ErrorHandler;

    public static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    async handleError(error: Error | string, context?: string): Promise<ErrorInfo> {
        const errorMessage = error instanceof Error ? error.message : error;
        const errorInfo = this.categorizeError(errorMessage, context);

        // Log the error
        console.error(`[Wakatimer Pro] ${context || 'Error'}:`, errorMessage);

        // Show appropriate user message
        await this.showUserMessage(errorInfo);

        return errorInfo;
    }

    private categorizeError(message: string, context?: string): ErrorInfo {
        // LLM API errors
        if (message.includes('Language Model API') || message.includes('Copilot')) {
            return {
                message: 'AI model is not available',
                severity: ErrorSeverity.ERROR,
                code: 'LLM_UNAVAILABLE',
                details: 'Please ensure you have access to GitHub Copilot or the VSCode Language Model API.',
                canRetry: true,
                canContinue: false
            };
        }

        // File system errors
        if (message.includes('ENOENT') || message.includes('file not found')) {
            return {
                message: 'File or directory not found',
                severity: ErrorSeverity.ERROR,
                code: 'FILE_NOT_FOUND',
                details: 'The specified file or directory could not be found.',
                canRetry: true,
                canContinue: true
            };
        }

        if (message.includes('EACCES') || message.includes('permission denied')) {
            return {
                message: 'Permission denied',
                severity: ErrorSeverity.ERROR,
                code: 'PERMISSION_DENIED',
                details: 'You do not have permission to access this file or directory.',
                canRetry: false,
                canContinue: false
            };
        }

        if (message.includes('EEXIST') || message.includes('already exists')) {
            return {
                message: 'File already exists',
                severity: ErrorSeverity.WARNING,
                code: 'FILE_EXISTS',
                details: 'The file you are trying to create already exists.',
                canRetry: false,
                canContinue: true
            };
        }

        // Parsing errors
        if (message.includes('parse') || message.includes('invalid format')) {
            return {
                message: 'Invalid format or parsing error',
                severity: ErrorSeverity.WARNING,
                code: 'PARSE_ERROR',
                details: 'The AI response could not be parsed correctly.',
                canRetry: true,
                canContinue: true
            };
        }

        // Network/timeout errors
        if (message.includes('timeout') || message.includes('network')) {
            return {
                message: 'Network or timeout error',
                severity: ErrorSeverity.WARNING,
                code: 'NETWORK_ERROR',
                details: 'A network error occurred. Please check your connection.',
                canRetry: true,
                canContinue: false
            };
        }

        // Validation errors
        if (message.includes('validation') || message.includes('invalid')) {
            return {
                message: 'Validation error',
                severity: ErrorSeverity.WARNING,
                code: 'VALIDATION_ERROR',
                details: 'The provided data failed validation.',
                canRetry: false,
                canContinue: true
            };
        }

        // Generic error
        return {
            message: 'An unexpected error occurred',
            severity: ErrorSeverity.ERROR,
            code: 'UNKNOWN_ERROR',
            details: message,
            canRetry: true,
            canContinue: false
        };
    }

    private async showUserMessage(errorInfo: ErrorInfo): Promise<void> {
        const actions: string[] = [];
        
        if (errorInfo.canRetry) {
            actions.push('Retry');
        }
        
        if (errorInfo.canContinue) {
            actions.push('Continue Anyway');
        }
        
        actions.push('Cancel');

        let showMethod: (message: string, ...items: string[]) => Thenable<string | undefined>;
        
        switch (errorInfo.severity) {
            case ErrorSeverity.INFO:
                showMethod = vscode.window.showInformationMessage;
                break;
            case ErrorSeverity.WARNING:
                showMethod = vscode.window.showWarningMessage;
                break;
            case ErrorSeverity.ERROR:
            case ErrorSeverity.CRITICAL:
                showMethod = vscode.window.showErrorMessage;
                break;
        }

        const fullMessage = errorInfo.details 
            ? `${errorInfo.message}\n\nDetails: ${errorInfo.details}`
            : errorInfo.message;

        await showMethod(fullMessage, ...actions);
    }

    createUserFriendlyMessage(error: Error | string, context?: string): string {
        const errorMessage = error instanceof Error ? error.message : error;
        
        // Common error patterns and their user-friendly messages
        const patterns = [
            {
                pattern: /Language Model API|Copilot/i,
                message: 'AI assistant is not available. Please ensure you have GitHub Copilot enabled.'
            },
            {
                pattern: /ENOENT|file not found/i,
                message: 'The specified file or folder could not be found.'
            },
            {
                pattern: /EACCES|permission denied/i,
                message: 'Permission denied. Please check file permissions.'
            },
            {
                pattern: /timeout/i,
                message: 'The operation timed out. Please try again.'
            },
            {
                pattern: /network|connection/i,
                message: 'Network connection error. Please check your internet connection.'
            }
        ];

        for (const { pattern, message } of patterns) {
            if (pattern.test(errorMessage)) {
                return message;
            }
        }

        return `An error occurred${context ? ` in ${context}` : ''}: ${errorMessage}`;
    }

    logError(error: Error | string, context?: string, additionalInfo?: any): void {
        const timestamp = new Date().toISOString();
        const errorMessage = error instanceof Error ? error.message : error;
        const stack = error instanceof Error ? error.stack : undefined;

        console.error(`[${timestamp}] Wakatimer Pro Error${context ? ` (${context})` : ''}:`);
        console.error(`Message: ${errorMessage}`);
        
        if (stack) {
            console.error(`Stack: ${stack}`);
        }
        
        if (additionalInfo) {
            console.error(`Additional Info:`, additionalInfo);
        }
    }
}
