# Wakatimer Pro

A professional VSCode extension for generating retroactive time tracking data. Perfect for developers who need to simulate development activity for time tracking applications like WakaTime.

## Features

- **🤖 AI-Powered**: Uses VSCode's Language Model API (GPT-4.1) to generate realistic code changes
- **📁 Flexible Modes**: Work in-place or copy files to a new location
- **⏰ Time Configuration**: Customize days, hours, and per-day schedules
- **🔄 Pause/Resume**: Full control over the execution process with re-verification
- **👁️ Preview Changes**: Review all changes before applying them
- **📊 Progress Tracking**: Real-time progress with detailed timeline view
- **🛡️ Error Handling**: Comprehensive error handling with user-friendly messages

## How It Works

Wakatimer Pro generates realistic code changes and applies them with 90-second intervals to simulate natural development activity. This creates authentic time tracking data for applications that monitor file changes.

## Installation

1. Clone this repository
2. Run `npm install` to install dependencies
3. Press `F5` to run the extension in a new Extension Development Host window
4. Open the Wakatimer Pro panel in the Explorer sidebar

## Usage

### Simple Mode (Recommended)

1. **Welcome Screen**: Click "Start" to begin
2. **Choose Mode**: Select "Edit in Place" or "Make a Copy and Edit"
3. **Directory Selection**: Choose your working directory
4. **File Copy** (if applicable): Select files to copy and customize
5. **Time Configuration**: Set days and hours per day
6. **Execution Mode**: Choose "Simple Mode" for automated AI processing
7. **Processing**: The AI analyzes your project and generates changes
8. **Preview**: Review the generated changes before applying
9. **Execution**: Changes are applied with 90-second intervals

### Advanced Mode

For users who prefer to use external AI tools:

1. Follow steps 1-6 above
2. Choose "Advanced Mode" in step 6
3. Copy the generated prompt to your preferred AI tool (Claude, ChatGPT, etc.)
4. Paste the AI response back into the extension
5. Continue with preview and execution

## Requirements

- VSCode 1.85.0 or higher
- GitHub Copilot subscription (for Simple Mode)
- Node.js 18.x or higher (for development)

## Extension Settings

This extension contributes the following settings:

- `wakatimerpro.defaultMode`: Default working mode (edit-in-place or copy-and-edit)
- `wakatimerpro.defaultHours`: Default hours per day
- `wakatimerpro.autoSave`: Automatically save files after changes

## Commands

- `Wakatimer Pro: Start`: Begin a new time tracking session
- `Wakatimer Pro: Open Folder`: Select a working directory
- `Wakatimer Pro: Refresh`: Refresh the side panel

## Development

### Setup

```bash
git clone https://github.com/wakatimerpro/wakatimerpro.git
cd wakatimerpro
npm install
```

### Building

```bash
npm run compile
```

### Testing

```bash
npm test
```

### Packaging

```bash
npm install -g vsce
vsce package
```

## Architecture

- **Core**: State management and extension lifecycle
- **Views**: UI components for each step of the process
- **Services**: Business logic for AI integration, file processing, and change application
- **Utils**: Shared utilities and error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

- GitHub Issues: [Report bugs or request features](https://github.com/wakatimerpro/wakatimerpro/issues)
- Documentation: [Full documentation](https://github.com/wakatimerpro/wakatimerpro/wiki)

## Changelog

### 1.0.0

- Initial release
- Simple and Advanced modes
- AI-powered change generation
- Pause/resume functionality
- Comprehensive error handling
- Preview and timeline features

---

**Note**: This extension is designed for legitimate time tracking purposes. Please ensure you comply with your organization's policies regarding time tracking and code modification.
