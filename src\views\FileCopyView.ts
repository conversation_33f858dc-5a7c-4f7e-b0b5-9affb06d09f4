import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { StateManager } from '../core/StateManager';

export class FileCopyView {
    private stateManager: StateManager;
    private sourceFiles: string[] = [];
    private showAdvanced: boolean = false;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('Select Source Directory', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Choose files to copy from';
        titleItem.iconPath = new vscode.ThemeIcon('folder-library');
        items.push(titleItem);

        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Source directory selection
        if (!state.sourceDirectory) {
            const selectSourceItem = new vscode.TreeItem('📁 Select Source Directory', vscode.TreeItemCollapsibleState.None);
            selectSourceItem.description = 'Choose directory to copy from';
            selectSourceItem.iconPath = new vscode.ThemeIcon('folder-opened');
            selectSourceItem.command = {
                command: 'wakatimerpro.selectSourceDirectory',
                title: 'Select Source Directory'
            };
            items.push(selectSourceItem);
        } else {
            // Show selected source directory
            const sourceItem = new vscode.TreeItem('📂 Source Directory', vscode.TreeItemCollapsibleState.None);
            sourceItem.description = state.sourceDirectory;
            sourceItem.iconPath = new vscode.ThemeIcon('folder-opened');
            items.push(sourceItem);

            // Load and show files
            await this.loadSourceFiles(state.sourceDirectory);

            if (this.sourceFiles.length > 0) {
                // Advanced options toggle
                const advancedItem = new vscode.TreeItem(
                    this.showAdvanced ? '🔽 Advanced Options' : '▶️ Advanced Options', 
                    vscode.TreeItemCollapsibleState.None
                );
                advancedItem.description = 'File selection options';
                advancedItem.iconPath = new vscode.ThemeIcon('settings-gear');
                advancedItem.command = {
                    command: 'wakatimerpro.toggleAdvanced',
                    title: 'Toggle Advanced Options'
                };
                items.push(advancedItem);

                // Show file list if advanced is open
                if (this.showAdvanced) {
                    items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
                    
                    const headerItem = new vscode.TreeItem('Files to Copy:', vscode.TreeItemCollapsibleState.None);
                    headerItem.iconPath = new vscode.ThemeIcon('list-unordered');
                    items.push(headerItem);

                    // Show first few files as preview
                    const previewFiles = this.sourceFiles.slice(0, 10);
                    for (const file of previewFiles) {
                        const isSelected = state.selectedFiles.includes(file);
                        const fileItem = new vscode.TreeItem(
                            `${isSelected ? '✅' : '⬜'} ${path.basename(file)}`,
                            vscode.TreeItemCollapsibleState.None
                        );
                        fileItem.description = path.relative(state.sourceDirectory!, file);
                        fileItem.iconPath = new vscode.ThemeIcon(isSelected ? 'check' : 'circle-outline');
                        fileItem.command = {
                            command: 'wakatimerpro.toggleFileSelection',
                            title: 'Toggle File Selection',
                            arguments: [file]
                        };
                        items.push(fileItem);
                    }

                    if (this.sourceFiles.length > 10) {
                        const moreItem = new vscode.TreeItem(`... and ${this.sourceFiles.length - 10} more files`, vscode.TreeItemCollapsibleState.None);
                        moreItem.iconPath = new vscode.ThemeIcon('ellipsis');
                        items.push(moreItem);
                    }

                    // Select all / none buttons
                    items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
                    
                    const selectAllItem = new vscode.TreeItem('✅ Select All', vscode.TreeItemCollapsibleState.None);
                    selectAllItem.command = {
                        command: 'wakatimerpro.selectAllFiles',
                        title: 'Select All Files'
                    };
                    items.push(selectAllItem);

                    const selectNoneItem = new vscode.TreeItem('❌ Select None', vscode.TreeItemCollapsibleState.None);
                    selectNoneItem.command = {
                        command: 'wakatimerpro.selectNoFiles',
                        title: 'Select No Files'
                    };
                    items.push(selectNoneItem);
                }

                // Next button
                items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
                
                const hasSelectedFiles = state.selectedFiles.length > 0;
                const nextItem = new vscode.TreeItem(
                    hasSelectedFiles ? '✅ Next' : '❌ Next (No files selected)',
                    vscode.TreeItemCollapsibleState.None
                );
                nextItem.description = hasSelectedFiles ? 'Copy files and continue' : 'Please select files first';
                nextItem.iconPath = new vscode.ThemeIcon(hasSelectedFiles ? 'arrow-right' : 'error');
                
                if (hasSelectedFiles) {
                    nextItem.command = {
                        command: 'wakatimerpro.copyFilesAndProceed',
                        title: 'Copy Files and Proceed'
                    };
                }
                items.push(nextItem);
            } else {
                const noFilesItem = new vscode.TreeItem('❌ No files found', vscode.TreeItemCollapsibleState.None);
                noFilesItem.description = 'Source directory is empty';
                noFilesItem.iconPath = new vscode.ThemeIcon('error');
                items.push(noFilesItem);
            }
        }

        // Back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to directory selection';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['directory-selection']
        };
        items.push(backItem);

        // Register commands
        this.registerCommands();

        return items;
    }

    private async loadSourceFiles(sourceDir: string): Promise<void> {
        try {
            this.sourceFiles = await this.getAllFiles(sourceDir);
            // Auto-select all files initially if none selected
            const state = this.stateManager.getState();
            if (state.selectedFiles.length === 0) {
                this.stateManager.setSelectedFiles(this.sourceFiles);
            }
        } catch (error) {
            console.error('Error loading source files:', error);
            this.sourceFiles = [];
        }
    }

    private async getAllFiles(dir: string): Promise<string[]> {
        const files: string[] = [];
        
        const items = await fs.promises.readdir(dir, { withFileTypes: true });
        
        for (const item of items) {
            const fullPath = path.join(dir, item.name);
            
            if (item.isDirectory()) {
                // Skip common directories that shouldn't be copied
                if (!['node_modules', '.git', '.vscode', 'dist', 'out', '.vscode-test'].includes(item.name)) {
                    const subFiles = await this.getAllFiles(fullPath);
                    files.push(...subFiles);
                }
            } else {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    private registerCommands(): void {
        vscode.commands.registerCommand('wakatimerpro.selectSourceDirectory', async () => {
            const folderUri = await vscode.window.showOpenDialog({
                canSelectFolders: true,
                canSelectFiles: false,
                canSelectMany: false,
                openLabel: 'Select Source Directory'
            });

            if (folderUri && folderUri[0]) {
                this.stateManager.setDirectories(folderUri[0].fsPath, this.stateManager.getState().destinationDirectory);
            }
        });

        vscode.commands.registerCommand('wakatimerpro.toggleAdvanced', () => {
            this.showAdvanced = !this.showAdvanced;
        });

        vscode.commands.registerCommand('wakatimerpro.toggleFileSelection', (filePath: string) => {
            const state = this.stateManager.getState();
            const selectedFiles = [...state.selectedFiles];
            const index = selectedFiles.indexOf(filePath);
            
            if (index > -1) {
                selectedFiles.splice(index, 1);
            } else {
                selectedFiles.push(filePath);
            }
            
            this.stateManager.setSelectedFiles(selectedFiles);
        });

        vscode.commands.registerCommand('wakatimerpro.selectAllFiles', () => {
            this.stateManager.setSelectedFiles([...this.sourceFiles]);
        });

        vscode.commands.registerCommand('wakatimerpro.selectNoFiles', () => {
            this.stateManager.setSelectedFiles([]);
        });

        vscode.commands.registerCommand('wakatimerpro.copyFilesAndProceed', async () => {
            await this.copySelectedFiles();
            this.stateManager.setCurrentStep('time-config');
        });
    }

    private async copySelectedFiles(): Promise<void> {
        const state = this.stateManager.getState();
        
        if (!state.sourceDirectory || !state.destinationDirectory) {
            return;
        }

        try {
            for (const filePath of state.selectedFiles) {
                const relativePath = path.relative(state.sourceDirectory, filePath);
                const destPath = path.join(state.destinationDirectory, relativePath);
                
                // Ensure destination directory exists
                await fs.promises.mkdir(path.dirname(destPath), { recursive: true });
                
                // Copy file
                await fs.promises.copyFile(filePath, destPath);
            }
            
            vscode.window.showInformationMessage(`Successfully copied ${state.selectedFiles.length} files.`);
        } catch (error) {
            vscode.window.showErrorMessage(`Error copying files: ${error}`);
        }
    }
}
