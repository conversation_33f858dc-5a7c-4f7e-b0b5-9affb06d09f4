{"version": 3, "file": "StateManager.js", "sourceRoot": "", "sources": ["../../src/core/StateManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAwBjC,MAAa,YAAY;IACb,KAAK,CAAW;IAChB,OAAO,CAA0B;IACjC,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAY,CAAC;IACnD,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEhE,YAAY,OAAgC;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEO,eAAe;QACnB,OAAO;YACH,WAAW,EAAE,SAAS;YACtB,IAAI,EAAE,IAAI;YACV,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;YAC1B,aAAa,EAAE,EAAE;YACjB,UAAU,EAAE;gBACR,IAAI,EAAE,CAAC;gBACP,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,EAAE;aAClB;YACD,aAAa,EAAE,IAAI;YACnB,OAAO,EAAE,EAAE;YACX,YAAY,EAAE,KAAK;YACnB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,CAAC;gBACV,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,EAAE;aACZ;SACJ,CAAC;IACN,CAAC;IAEO,SAAS;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAoB,oBAAoB,CAAC,CAAC;QACzF,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,UAAU,EAAE,CAAC;QAClD,CAAC;IACL,CAAC;IAEO,SAAS;QACb,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC;IAEM,QAAQ;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEM,WAAW,CAAC,OAA0B;QACzC,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEM,UAAU;QACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEM,cAAc,CAAC,IAA6B;QAC/C,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEM,OAAO,CAAC,IAAsB;QACjC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEM,cAAc,CAAC,MAAqB,EAAE,WAA0B;QACnE,IAAI,CAAC,WAAW,CAAC;YACb,eAAe,EAAE,MAAM;YACvB,oBAAoB,EAAE,WAAW;SACpC,CAAC,CAAC;IACP,CAAC;IAEM,gBAAgB,CAAC,KAAe;QACnC,IAAI,CAAC,WAAW,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEM,aAAa,CAAC,MAAuC;QACxD,IAAI,CAAC,WAAW,CAAC;YACb,UAAU,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,MAAM,EAAE;SACtD,CAAC,CAAC;IACP,CAAC;IAEM,gBAAgB,CAAC,IAA+B;QACnD,IAAI,CAAC,WAAW,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9C,CAAC;IAEM,UAAU,CAAC,OAAc;QAC5B,IAAI,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IAClC,CAAC;IAEM,aAAa,CAAC,YAAqB;QACtC,IAAI,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;IACvC,CAAC;IAEM,QAAQ,CAAC,KAAoB;QAChC,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAChC,CAAC;IAEM,WAAW,CAAC,QAAuC;QACtD,IAAI,CAAC,WAAW,CAAC;YACb,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE;SACpD,CAAC,CAAC;IACP,CAAC;CACJ;AA7GD,oCA6GC"}