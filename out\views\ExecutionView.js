"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionView = void 0;
const vscode = __importStar(require("vscode"));
const WakatimerOrchestrator_1 = require("../services/WakatimerOrchestrator");
class ExecutionView {
    stateManager;
    orchestrator;
    isPaused = false;
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.orchestrator = new WakatimerOrchestrator_1.WakatimerOrchestrator(stateManager);
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title
        const titleItem = new vscode.TreeItem('Executing Changes', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Applying changes with 90s intervals';
        titleItem.iconPath = new vscode.ThemeIcon('play');
        items.push(titleItem);
        // Progress
        if (state.progress.total > 0) {
            const progressItem = new vscode.TreeItem(`Progress: ${state.progress.current}/${state.progress.total}`, vscode.TreeItemCollapsibleState.None);
            progressItem.description = `${Math.round((state.progress.current / state.progress.total) * 100)}% complete`;
            progressItem.iconPath = new vscode.ThemeIcon('graph');
            items.push(progressItem);
        }
        // Current change
        if (state.progress.stage) {
            const currentItem = new vscode.TreeItem(`Current: ${state.progress.stage}`, vscode.TreeItemCollapsibleState.None);
            currentItem.iconPath = new vscode.ThemeIcon('edit');
            items.push(currentItem);
        }
        // Control buttons
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        if (this.isPaused) {
            const resumeItem = new vscode.TreeItem('▶️ Resume', vscode.TreeItemCollapsibleState.None);
            resumeItem.description = 'Continue execution';
            resumeItem.iconPath = new vscode.ThemeIcon('play');
            resumeItem.command = {
                command: 'wakatimerpro.resumeExecution',
                title: 'Resume Execution'
            };
            items.push(resumeItem);
        }
        else {
            const pauseItem = new vscode.TreeItem('⏸️ Pause', vscode.TreeItemCollapsibleState.None);
            pauseItem.description = 'Pause execution';
            pauseItem.iconPath = new vscode.ThemeIcon('debug-pause');
            pauseItem.command = {
                command: 'wakatimerpro.pauseExecution',
                title: 'Pause Execution'
            };
            items.push(pauseItem);
        }
        const stopItem = new vscode.TreeItem('⏹️ Stop', vscode.TreeItemCollapsibleState.None);
        stopItem.description = 'Stop execution';
        stopItem.iconPath = new vscode.ThemeIcon('stop');
        stopItem.command = {
            command: 'wakatimerpro.stopExecution',
            title: 'Stop Execution'
        };
        items.push(stopItem);
        // Timeline preview
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const timelineItem = new vscode.TreeItem('📋 Timeline', vscode.TreeItemCollapsibleState.None);
        timelineItem.description = 'View change timeline';
        timelineItem.iconPath = new vscode.ThemeIcon('list-ordered');
        timelineItem.command = {
            command: 'wakatimerpro.showTimeline',
            title: 'Show Timeline'
        };
        items.push(timelineItem);
        // Register commands
        this.registerCommands();
        return items;
    }
    registerCommands() {
        vscode.commands.registerCommand('wakatimerpro.pauseExecution', () => {
            this.isPaused = true;
            this.orchestrator.pauseExecution();
        });
        vscode.commands.registerCommand('wakatimerpro.resumeExecution', () => {
            this.isPaused = false;
            this.orchestrator.resumeExecution();
        });
        vscode.commands.registerCommand('wakatimerpro.stopExecution', () => {
            this.orchestrator.stopExecution();
        });
        vscode.commands.registerCommand('wakatimerpro.showTimeline', async () => {
            const state = this.stateManager.getState();
            // Create timeline view
            const timelineContent = this.generateTimelineContent(state.changes, state.progress);
            const document = await vscode.workspace.openTextDocument({
                content: timelineContent,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(document, { preview: true });
        });
    }
    generateTimelineContent(changes, progress) {
        const lines = [
            '# Wakatimer Pro - Execution Timeline',
            '',
            `Progress: ${progress.current}/${progress.total} changes applied`,
            `Current stage: ${progress.stage}`,
            '',
            '## Timeline:',
            ''
        ];
        changes.forEach((change, index) => {
            const status = index < progress.current ? '✅' : index === progress.current ? '🔄' : '⏳';
            lines.push(`${status} **Change ${index + 1}**: ${change.description}`);
            lines.push(`   File: \`${change.file}\``);
            lines.push(`   Type: ${change.type}`);
            lines.push('');
        });
        return lines.join('\n');
    }
}
exports.ExecutionView = ExecutionView;
//# sourceMappingURL=ExecutionView.js.map