{"version": 3, "file": "DiffParser.js", "sourceRoot": "", "sources": ["../../src/services/DiffParser.ts"], "names": [], "mappings": ";;;AA0BA,MAAa,UAAU;IACnB,YAAY,CAAC,WAAmB;QAC5B,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,wCAAwC;QACxC,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;QAC/F,CAAC;QAED,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAEvC,2BAA2B;QAC3B,MAAM,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,4EAA4E,CAAC,CAAC;QAE5H,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAChC,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;YAE1C,sBAAsB;YACtB,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEzD,eAAe;YACf,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YACxD,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAElD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,CAAC,IAAI,CAAC;oBACT,EAAE;oBACF,IAAI;oBACJ,IAAI,EAAE,IAAW;oBACjB,WAAW;oBACX,IAAI,EAAE,EAAE;oBACR,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB;iBACjC,CAAC,CAAC;gBACH,SAAS;YACb,CAAC;YAED,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACnC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAW,EAAE,KAAK,CAAC,CAAC;gBAE9D,OAAO,CAAC,IAAI,CAAC;oBACT,EAAE;oBACF,IAAI;oBACJ,IAAI,EAAE,IAAW;oBACjB,WAAW;oBACX,IAAI;oBACJ,KAAK;oBACL,OAAO;oBACP,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gCAAgC;iBAChE,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC;oBACT,EAAE;oBACF,IAAI;oBACJ,IAAI,EAAE,IAAW;oBACjB,WAAW;oBACX,IAAI;oBACJ,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gBAAgB,KAAK,EAAE;iBACjC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,SAAS,CAAC,WAAmB;QACjC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,IAAI,WAAW,GAAoB,IAAI,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,oBAAoB;YACpB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,SAAS;YACb,CAAC;YAED,oBAAoB;YACpB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YAClF,IAAI,SAAS,EAAE,CAAC;gBACZ,IAAI,WAAW,EAAE,CAAC;oBACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC5B,CAAC;gBAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3D,WAAW,GAAG;oBACV,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR,KAAK,EAAE,EAAE;iBACZ,CAAC;gBACF,SAAS;YACb,CAAC;YAED,mBAAmB;YACnB,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE9B,IAAI,IAAsB,CAAC;gBAC3B,QAAQ,SAAS,EAAE,CAAC;oBAChB,KAAK,GAAG;wBACJ,IAAI,GAAG,UAAU,CAAC;wBAClB,MAAM;oBACV,KAAK,GAAG;wBACJ,IAAI,GAAG,UAAU,CAAC;wBAClB,MAAM;oBACV,KAAK,GAAG;wBACJ,IAAI,GAAG,SAAS,CAAC;wBACjB,MAAM;oBACV;wBACI,qBAAqB;wBACrB,SAAS;gBACjB,CAAC;gBAED,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBACnB,IAAI;oBACJ,OAAO;iBACV,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,cAAc,CAAC,IAAY,EAAE,IAA0B,EAAE,KAAiB;QAC9E,mBAAmB;QACnB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACtB,sDAAsD;YACtD,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACtB,+CAA+C;YAC/C,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAChF,CAAC;QACN,CAAC;QAED,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;YAC1B,iEAAiE;YACjE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,CAChF,CAAC;QACN,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,kBAAkB,CAAC,OAAuB;QACtC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE1D,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;IAC9B,CAAC;IAED,eAAe,CAAC,OAAuB;QACnC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG;YACZ,kBAAkB,OAAO,CAAC,MAAM,EAAE;YAClC,kBAAkB,KAAK,CAAC,MAAM,EAAE;YAChC,oBAAoB,OAAO,CAAC,MAAM,EAAE;YACpC,EAAE;YACF,kBAAkB;YAClB,oBAAoB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC,MAAM,EAAE;YACzE,gBAAgB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,MAAM,EAAE;YACjE,gBAAgB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,MAAM,EAAE;SACpE,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;YACrC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;CACJ;AAvMD,gCAuMC"}