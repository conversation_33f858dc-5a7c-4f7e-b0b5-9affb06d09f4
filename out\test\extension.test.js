"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const StateManager_1 = require("../core/StateManager");
const DiffParser_1 = require("../services/DiffParser");
const ProjectAnalyzer_1 = require("../services/ProjectAnalyzer");
suite('Wakatimer Pro Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('StateManager initialization', () => {
        const mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve()
            }
        };
        const stateManager = new StateManager_1.StateManager(mockContext);
        const state = stateManager.getState();
        assert.strictEqual(state.currentStep, 'welcome');
        assert.strictEqual(state.mode, null);
        assert.strictEqual(state.timeConfig.days, 1);
        assert.strictEqual(state.timeConfig.hoursPerDay, 8);
    });
    test('DiffParser parses valid changes', () => {
        const parser = new DiffParser_1.DiffParser();
        const mockResponse = `
<changes>
<change id="1" file="test.js" type="modification">
<description>Add console.log statement</description>
<diff>
--- a/test.js
+++ b/test.js
@@ -1,3 +1,4 @@
 function test() {
+    console.log('Hello World');
     return true;
 }
</diff>
</change>
</changes>
        `;
        const changes = parser.parseChanges(mockResponse);
        assert.strictEqual(changes.length, 1);
        assert.strictEqual(changes[0].id, '1');
        assert.strictEqual(changes[0].file, 'test.js');
        assert.strictEqual(changes[0].type, 'modification');
        assert.strictEqual(changes[0].description, 'Add console.log statement');
    });
    test('DiffParser handles invalid format', () => {
        const parser = new DiffParser_1.DiffParser();
        const invalidResponse = 'This is not a valid response';
        assert.throws(() => {
            parser.parseChanges(invalidResponse);
        }, /No changes found in LLM response/);
    });
    test('ProjectAnalyzer filters ignored directories', async () => {
        const analyzer = new ProjectAnalyzer_1.ProjectAnalyzer();
        // Test the private method through reflection (for testing purposes)
        const shouldIgnore = analyzer.shouldIgnoreDirectory;
        assert.strictEqual(shouldIgnore('node_modules'), true);
        assert.strictEqual(shouldIgnore('.git'), true);
        assert.strictEqual(shouldIgnore('.vscode'), true);
        assert.strictEqual(shouldIgnore('src'), false);
        assert.strictEqual(shouldIgnore('lib'), false);
    });
    test('StateManager updates state correctly', () => {
        const mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve()
            }
        };
        const stateManager = new StateManager_1.StateManager(mockContext);
        stateManager.setMode('edit-in-place');
        stateManager.setTimeConfig({ days: 5, hoursPerDay: 6 });
        const state = stateManager.getState();
        assert.strictEqual(state.mode, 'edit-in-place');
        assert.strictEqual(state.timeConfig.days, 5);
        assert.strictEqual(state.timeConfig.hoursPerDay, 6);
    });
    test('DiffParser validates changes correctly', () => {
        const parser = new DiffParser_1.DiffParser();
        const mockResponse = `
<changes>
<change id="1" file="test.js" type="modification">
<description>Valid change</description>
<diff>
--- a/test.js
+++ b/test.js
@@ -1,2 +1,3 @@
 line1
+added line
 line2
</diff>
</change>
<change id="2" file="invalid.js" type="modification">
<description>Invalid change - no diff</description>
<diff></diff>
</change>
</changes>
        `;
        const changes = parser.parseChanges(mockResponse);
        const { valid, invalid } = parser.validateAllChanges(changes);
        assert.strictEqual(valid.length, 1);
        assert.strictEqual(invalid.length, 1);
        assert.strictEqual(valid[0].id, '1');
        assert.strictEqual(invalid[0].id, '2');
    });
});
//# sourceMappingURL=extension.test.js.map