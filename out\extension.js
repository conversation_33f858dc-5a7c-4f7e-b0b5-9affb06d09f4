"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const WakatimerProProvider_1 = require("./providers/WakatimerProProvider");
const StateManager_1 = require("./core/StateManager");
function activate(context) {
    console.log('Wakatimer Pro extension is now active!');
    // Initialize state manager
    const stateManager = new StateManager_1.StateManager(context);
    // Create and register the side panel provider
    const provider = new WakatimerProProvider_1.WakatimerProProvider(context, stateManager);
    vscode.window.registerTreeDataProvider('wakatimerpro.sidePanel', provider);
    // Register all commands centrally to avoid duplicates
    registerAllCommands(context, stateManager, provider);
    // Initialize the provider
    provider.refresh();
}
function registerAllCommands(context, stateManager, provider) {
    // Helper function to safely register commands
    const safeRegisterCommand = (commandId, callback) => {
        try {
            const disposable = vscode.commands.registerCommand(commandId, callback);
            context.subscriptions.push(disposable);
        }
        catch (error) {
            console.log(`Command ${commandId} already registered, skipping...`);
        }
    };
    // Core commands
    safeRegisterCommand('wakatimerpro.openFolderDialog', async () => {
        const folderUri = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Folder'
        });
        if (folderUri && folderUri[0]) {
            await provider.handleFolderSelection(folderUri[0]);
        }
    });
    safeRegisterCommand('wakatimerpro.refresh', () => {
        provider.refresh();
    });
    // Navigation commands
    safeRegisterCommand('wakatimerpro.start', () => {
        stateManager.setCurrentStep('mode-selection');
    });
    safeRegisterCommand('wakatimerpro.selectMode', (mode) => {
        stateManager.setMode(mode);
        stateManager.setCurrentStep('directory-selection');
    });
    safeRegisterCommand('wakatimerpro.goBack', (step) => {
        stateManager.setCurrentStep(step);
    });
    safeRegisterCommand('wakatimerpro.proceedFromDirectory', (directoryPath) => {
        const state = stateManager.getState();
        if (state.mode === 'edit-in-place') {
            stateManager.setDirectories(null, directoryPath);
            stateManager.setCurrentStep('time-config');
        }
        else {
            stateManager.setDirectories(null, directoryPath);
            stateManager.setCurrentStep('file-copy');
        }
    });
    // Time configuration commands
    safeRegisterCommand('wakatimerpro.configureDays', async () => {
        const state = stateManager.getState();
        const input = await vscode.window.showInputBox({
            prompt: 'Enter number of days (1-30)',
            value: state.timeConfig.days.toString(),
            validateInput: (value) => {
                const num = parseInt(value);
                if (isNaN(num) || num < 1 || num > 30) {
                    return 'Please enter a number between 1 and 30';
                }
                return null;
            }
        });
        if (input) {
            const days = parseInt(input);
            stateManager.setTimeConfig({ days });
        }
    });
    safeRegisterCommand('wakatimerpro.configureHours', async () => {
        const state = stateManager.getState();
        const input = await vscode.window.showInputBox({
            prompt: 'Enter hours per day (1-24)',
            value: state.timeConfig.hoursPerDay.toString(),
            validateInput: (value) => {
                const num = parseInt(value);
                if (isNaN(num) || num < 1 || num > 24) {
                    return 'Please enter a number between 1 and 24';
                }
                return null;
            }
        });
        if (input) {
            const hoursPerDay = parseInt(input);
            stateManager.setTimeConfig({ hoursPerDay });
        }
    });
    safeRegisterCommand('wakatimerpro.proceedFromTimeConfig', () => {
        stateManager.setCurrentStep('execution-mode');
    });
    safeRegisterCommand('wakatimerpro.toggleTimeAdvanced', () => {
        // This will be handled by the view's internal state
        provider.refresh();
    });
    safeRegisterCommand('wakatimerpro.configureCustomHours', async (day) => {
        const state = stateManager.getState();
        const currentHours = state.timeConfig.customHours[day] || state.timeConfig.hoursPerDay;
        const input = await vscode.window.showInputBox({
            prompt: `Enter hours for day ${day} (0-24)`,
            value: currentHours.toString(),
            validateInput: (value) => {
                const num = parseInt(value);
                if (isNaN(num) || num < 0 || num > 24) {
                    return 'Please enter a number between 0 and 24';
                }
                return null;
            }
        });
        if (input) {
            const hours = parseInt(input);
            const customHours = { ...state.timeConfig.customHours };
            customHours[day] = hours;
            stateManager.setTimeConfig({ customHours });
        }
    });
    // Execution mode commands
    safeRegisterCommand('wakatimerpro.selectExecutionMode', (mode) => {
        stateManager.setExecutionMode(mode);
        stateManager.setCurrentStep('processing');
    });
    // Processing commands
    safeRegisterCommand('wakatimerpro.retryProcessing', () => {
        stateManager.setError(null);
        // Processing will restart automatically when error is cleared
    });
    safeRegisterCommand('wakatimerpro.continueWithError', () => {
        stateManager.setError(null);
        stateManager.setCurrentStep('preview');
    });
    // Preview commands
    safeRegisterCommand('wakatimerpro.previewChanges', async () => {
        const state = stateManager.getState();
        // Create a preview document showing all changes
        const previewContent = generatePreviewContent(state.changes);
        const document = await vscode.workspace.openTextDocument({
            content: previewContent,
            language: 'diff'
        });
        await vscode.window.showTextDocument(document, { preview: true });
    });
    safeRegisterCommand('wakatimerpro.startExecution', () => {
        stateManager.setCurrentStep('execution');
    });
    // Execution commands
    safeRegisterCommand('wakatimerpro.pauseExecution', () => {
        // Pause logic will be handled by the orchestrator
    });
    safeRegisterCommand('wakatimerpro.resumeExecution', () => {
        // Resume logic will be handled by the orchestrator
    });
    safeRegisterCommand('wakatimerpro.stopExecution', () => {
        stateManager.setCurrentStep('complete');
    });
    // Complete commands
    safeRegisterCommand('wakatimerpro.startNewSession', () => {
        stateManager.resetState();
    });
}
function generatePreviewContent(changes) {
    const lines = [
        '# Wakatimer Pro - Changes Preview',
        '',
        `Total changes: ${changes.length}`,
        '',
        '## Changes to be applied:',
        ''
    ];
    changes.forEach((change, index) => {
        lines.push(`### Change ${index + 1}: ${change.description || 'No description'}`);
        lines.push(`File: ${change.file || 'Unknown file'}`);
        lines.push(`Type: ${change.type || 'Unknown type'}`);
        lines.push('');
        lines.push('```diff');
        lines.push(change.diff || 'No diff available');
        lines.push('```');
        lines.push('');
    });
    return lines.join('\n');
}
function deactivate() {
    console.log('Wakatimer Pro extension is now deactivated!');
}
//# sourceMappingURL=extension.js.map