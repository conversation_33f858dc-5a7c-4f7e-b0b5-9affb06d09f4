"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const WakatimerProProvider_1 = require("./providers/WakatimerProProvider");
const StateManager_1 = require("./core/StateManager");
function activate(context) {
    console.log('Wakatimer Pro extension is now active!');
    // Initialize state manager
    const stateManager = new StateManager_1.StateManager(context);
    // Create and register the side panel provider
    const provider = new WakatimerProProvider_1.WakatimerProProvider(context, stateManager);
    vscode.window.registerTreeDataProvider('wakatimerpro.sidePanel', provider);
    // Register commands
    const openFolderCommand = vscode.commands.registerCommand('wakatimerpro.openFolderDialog', async () => {
        const folderUri = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Folder'
        });
        if (folderUri && folderUri[0]) {
            await provider.handleFolderSelection(folderUri[0]);
        }
    });
    const refreshCommand = vscode.commands.registerCommand('wakatimerpro.refresh', () => {
        provider.refresh();
    });
    // Add commands to context subscriptions
    context.subscriptions.push(openFolderCommand, refreshCommand);
    // Initialize the provider
    provider.refresh();
}
function deactivate() {
    console.log('Wakatimer Pro extension is now deactivated!');
}
//# sourceMappingURL=extension.js.map