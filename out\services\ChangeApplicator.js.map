{"version": 3, "file": "ChangeApplicator.js", "sourceRoot": "", "sources": ["../../src/services/ChangeApplicator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAU7B,MAAa,gBAAgB;IACjB,QAAQ,GAAY,KAAK,CAAC;IAC1B,SAAS,GAAY,KAAK,CAAC;IAEnC,KAAK,CAAC,YAAY,CACd,OAAuB,EACvB,QAAgB,EAChB,gBAA0E,EAC1E,kBAAkC;QAElC,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,6BAA6B;YAC7B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM;YACV,CAAC;YAED,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,EAAE,CAAC,EAAE,CAAC;gBACnE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;gBAC7C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,MAAM;gBACV,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM;YACV,CAAC;YAED,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE1B,IAAI,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,oBAAoB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;YAED,8EAA8E;YAC9E,IAAI,CAAC;gBACD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC3E,IAAI,CAAC,YAAY,EAAE,CAAC;oBAChB,OAAO,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,kCAAkC,CAAC,CAAC;oBACpE,SAAS;gBACb,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7D,SAAS;YACb,CAAC;YAED,IAAI,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,mBAAmB,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;YACjG,CAAC;YAED,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACzC,cAAc,EAAE,CAAC;gBAEjB,4DAA4D;gBAC5D,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;gBAC1C,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5D,0DAA0D;YAC9D,CAAC;QACL,CAAC;QAED,OAAO;YACH,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,cAAc,KAAK,YAAY;YAC3D,cAAc;YACd,YAAY;YACZ,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS;SACpE,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAoB,EAAE,QAAgB;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAElD,8BAA8B;QAC9B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAEtC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,UAAU;gBACX,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC3C,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAChD,MAAM;YACV,KAAK,UAAU;gBACX,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAChC,MAAM;QACd,CAAC;QAED,gBAAgB;QAChB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACnE,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,2CAA2C;YAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,6BAA6B,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,MAAoB;QAC9D,0BAA0B;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAElD,6BAA6B;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE/D,aAAa;QACb,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,MAAoB;QACnE,wBAAwB;QACxB,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEtE,aAAa;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAE1E,yBAAyB;QACzB,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACrC,IAAI,CAAC;YACD,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,KAAiB,EAAE,WAAmB;QAClE,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACtD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC7B,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEO,kBAAkB,CAAC,OAAe,EAAE,KAAiB;QACzD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,2BAA2B;YACzE,IAAI,WAAW,GAAG,SAAS,CAAC;YAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,+BAA+B;YAC/B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChC,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC/B,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBAC7B,SAAS,EAAE,CAAC;gBAChB,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACrC,WAAW,EAAE,CAAC;gBAClB,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACtC,4CAA4C;gBAChD,CAAC;YACL,CAAC;YAED,gCAAgC;YAChC,WAAW,GAAG,SAAS,CAAC;YACxB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChC,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC/B,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC/C,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,CAAC;gBAChB,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACrC,WAAW,EAAE,CAAC;gBAClB,CAAC;YACL,CAAC;YAED,qCAAqC;YACrC,MAAM,IAAI,SAAS,GAAG,SAAS,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,EAAU;QACpB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,MAAM;QACF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,aAAa;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,cAAc;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,MAAoB,EAAE,QAAgB;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC7B,2DAA2D;gBAC3D,IAAI,CAAC;oBACD,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACnC,OAAO,KAAK,CAAC,CAAC,sBAAsB;gBACxC,CAAC;gBAAC,MAAM,CAAC;oBACL,OAAO,IAAI,CAAC,CAAC,oCAAoC;gBACrD,CAAC;YACL,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC7B,4CAA4C;gBAC5C,IAAI,CAAC;oBACD,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,CAAC,6BAA6B;gBAC9C,CAAC;gBAAC,MAAM,CAAC;oBACL,OAAO,KAAK,CAAC,CAAC,qBAAqB;gBACvC,CAAC;YACL,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACjC,6EAA6E;gBAC7E,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAC9D,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC9D,CAAC;gBAAC,MAAM,CAAC;oBACL,OAAO,KAAK,CAAC,CAAC,sCAAsC;gBACxD,CAAC;YACL,CAAC;YAED,OAAO,KAAK,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAe,EAAE,KAAiB;QAC7D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,2BAA2B;YAEhE,4DAA4D;YAC5D,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC7C,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,WAAW,GAAG,SAAS,CAAC;YAC5B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChC,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC9D,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;wBACzE,OAAO,KAAK,CAAC,CAAC,wBAAwB;oBAC1C,CAAC;oBACD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;wBAC9B,WAAW,EAAE,CAAC;oBAClB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAhSD,4CAgSC"}