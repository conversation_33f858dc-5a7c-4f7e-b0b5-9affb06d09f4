"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectorySelectionView = void 0;
const vscode = __importStar(require("vscode"));
const fs = __importStar(require("fs"));
class DirectorySelectionView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title based on mode
        const titleText = state.mode === 'edit-in-place'
            ? 'Select Working Directory'
            : 'Select Destination Directory';
        const titleItem = new vscode.TreeItem(titleText, vscode.TreeItemCollapsibleState.None);
        titleItem.description = state.mode === 'edit-in-place'
            ? 'Choose the directory to work with'
            : 'Choose where to copy files to';
        titleItem.iconPath = new vscode.ThemeIcon('folder');
        items.push(titleItem);
        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        // Current workspace info
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            const currentDirItem = new vscode.TreeItem('📂 Current Directory', vscode.TreeItemCollapsibleState.None);
            currentDirItem.description = workspaceFolder.uri.fsPath;
            currentDirItem.iconPath = new vscode.ThemeIcon('folder-opened');
            currentDirItem.tooltip = `Current workspace: ${workspaceFolder.uri.fsPath}`;
            items.push(currentDirItem);
            // Check if directory is empty (for copy mode)
            if (state.mode === 'copy-and-edit') {
                const isEmpty = await this.isDirectoryEmpty(workspaceFolder.uri.fsPath);
                if (!isEmpty) {
                    const warningItem = new vscode.TreeItem('⚠️ Warning', vscode.TreeItemCollapsibleState.None);
                    warningItem.description = 'Directory is not empty';
                    warningItem.iconPath = new vscode.ThemeIcon('warning');
                    warningItem.tooltip = 'The destination directory should be empty for copy mode';
                    items.push(warningItem);
                }
            }
            // Next button (disabled if copy mode and directory not empty)
            const canProceed = state.mode === 'edit-in-place' || await this.isDirectoryEmpty(workspaceFolder.uri.fsPath);
            const nextItem = new vscode.TreeItem(canProceed ? '✅ Next' : '❌ Next (Disabled)', vscode.TreeItemCollapsibleState.None);
            nextItem.description = canProceed ? 'Continue to next step' : 'Directory must be empty';
            nextItem.iconPath = new vscode.ThemeIcon(canProceed ? 'arrow-right' : 'error');
            if (canProceed) {
                nextItem.command = {
                    command: 'wakatimerpro.proceedFromDirectory',
                    title: 'Next',
                    arguments: [workspaceFolder.uri.fsPath]
                };
            }
            items.push(nextItem);
        }
        else {
            // No workspace open
            const noWorkspaceItem = new vscode.TreeItem('❌ No Folder Open', vscode.TreeItemCollapsibleState.None);
            noWorkspaceItem.description = 'Please open a folder first';
            noWorkspaceItem.iconPath = new vscode.ThemeIcon('error');
            items.push(noWorkspaceItem);
            // Open folder button
            const openFolderItem = new vscode.TreeItem('📁 Open Folder', vscode.TreeItemCollapsibleState.None);
            openFolderItem.description = 'Select a folder to work with';
            openFolderItem.iconPath = new vscode.ThemeIcon('folder-opened');
            openFolderItem.command = {
                command: 'wakatimerpro.openFolderDialog',
                title: 'Open Folder'
            };
            items.push(openFolderItem);
        }
        // Spacer and back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to mode selection';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['mode-selection']
        };
        items.push(backItem);
        return items;
    }
    async isDirectoryEmpty(dirPath) {
        try {
            const files = await fs.promises.readdir(dirPath);
            return files.length === 0;
        }
        catch (error) {
            return false;
        }
    }
    async handleFolderSelection(folderUri) {
        // This will be called when a folder is selected via the open dialog
        const state = this.stateManager.getState();
        if (state.mode === 'copy-and-edit') {
            const isEmpty = await this.isDirectoryEmpty(folderUri.fsPath);
            if (!isEmpty) {
                vscode.window.showWarningMessage('The selected directory is not empty. Please choose an empty directory for copy mode.');
                return;
            }
        }
        // Update the workspace
        await vscode.commands.executeCommand('vscode.openFolder', folderUri);
    }
}
exports.DirectorySelectionView = DirectorySelectionView;
//# sourceMappingURL=DirectorySelectionView.js.map