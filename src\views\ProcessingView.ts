import * as vscode from 'vscode';
import { StateManager } from '../core/StateManager';
import { WakatimerOrchestrator } from '../services/WakatimerOrchestrator';

export class ProcessingView {
    private stateManager: StateManager;
    private orchestrator: WakatimerOrchestrator;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
        this.orchestrator = new WakatimerOrchestrator(stateManager);

        // Check if processing is already in progress to prevent infinite loop
        const state = stateManager.getState();
        if (!state.isProcessing) {
            this.startProcessing();
        }
    }

    async getItems(): Promise<vscode.TreeItem[]> {
        const items: vscode.TreeItem[] = [];
        const state = this.stateManager.getState();

        // Title
        const titleItem = new vscode.TreeItem('Processing...', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Generating changes';
        titleItem.iconPath = new vscode.ThemeIcon('loading~spin');
        items.push(titleItem);

        // Progress indicator
        if (state.progress.total > 0) {
            const progressItem = new vscode.TreeItem(
                `Progress: ${state.progress.current}/${state.progress.total}`,
                vscode.TreeItemCollapsibleState.None
            );
            progressItem.description = state.progress.stage;
            progressItem.iconPath = new vscode.ThemeIcon('graph');
            items.push(progressItem);
        }

        // Current stage
        if (state.progress.stage) {
            const stageItem = new vscode.TreeItem(state.progress.stage, vscode.TreeItemCollapsibleState.None);
            stageItem.iconPath = new vscode.ThemeIcon('gear');
            items.push(stageItem);
        }

        // Error handling
        if (state.error) {
            items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

            const errorItem = new vscode.TreeItem('❌ Error Occurred', vscode.TreeItemCollapsibleState.None);
            errorItem.description = state.error;
            errorItem.iconPath = new vscode.ThemeIcon('error');
            items.push(errorItem);

            // Retry button
            const retryItem = new vscode.TreeItem('🔄 Retry', vscode.TreeItemCollapsibleState.None);
            retryItem.description = 'Try again';
            retryItem.iconPath = new vscode.ThemeIcon('refresh');
            retryItem.command = {
                command: 'wakatimerpro.retryProcessing',
                title: 'Retry Processing'
            };
            items.push(retryItem);

            // Continue anyway button (if applicable)
            const continueItem = new vscode.TreeItem('⚠️ Continue Anyway', vscode.TreeItemCollapsibleState.None);
            continueItem.description = 'Proceed with partial results';
            continueItem.iconPath = new vscode.ThemeIcon('warning');
            continueItem.command = {
                command: 'wakatimerpro.continueWithError',
                title: 'Continue with Error'
            };
            items.push(continueItem);
        }

        // Always show control buttons
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));

        // Reset button
        const resetItem = new vscode.TreeItem('🔄 Start Over', vscode.TreeItemCollapsibleState.None);
        resetItem.description = 'Reset and start from beginning';
        resetItem.iconPath = new vscode.ThemeIcon('debug-restart');
        resetItem.command = {
            command: 'wakatimerpro.resetAndStartOver',
            title: 'Reset and Start Over'
        };
        items.push(resetItem);

        // Back button
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to execution mode selection';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['execution-mode']
        };
        items.push(backItem);

        return items;
    }

    private async startProcessing(): Promise<void> {
        const state = this.stateManager.getState();

        if (state.executionMode === 'simple') {
            await this.orchestrator.executeSimpleMode();
        } else if (state.executionMode === 'advanced') {
            try {
                const prompt = await this.orchestrator.executeAdvancedMode();
                this.showAdvancedModePrompt(prompt);
            } catch (error) {
                this.stateManager.setError(`Failed to generate prompt: ${error}`);
            }
        }
    }

    private showAdvancedModePrompt(prompt: string): void {
        // Create a new document with the prompt
        vscode.workspace.openTextDocument({
            content: prompt,
            language: 'markdown'
        }).then(document => {
            vscode.window.showTextDocument(document);

            // Show instructions to user
            vscode.window.showInformationMessage(
                'Copy this prompt to your preferred AI tool (Claude, ChatGPT, etc.), get the response, and paste it back using the "Process Response" button.',
                'Show Response Input'
            ).then(selection => {
                if (selection === 'Show Response Input') {
                    this.showResponseInput();
                }
            });
        });
    }

    private showResponseInput(): void {
        vscode.window.showInputBox({
            prompt: 'Paste the AI response here',
            placeHolder: 'Paste the complete response from your AI tool...',
            ignoreFocusOut: true
        }).then(response => {
            if (response) {
                this.orchestrator.processAdvancedModeResponse(response);
            }
        });
    }


}
