"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StateManager = void 0;
const vscode = __importStar(require("vscode"));
class StateManager {
    state;
    context;
    onStateChangeEmitter = new vscode.EventEmitter();
    onStateChange = this.onStateChangeEmitter.event;
    constructor(context) {
        this.context = context;
        this.state = this.getInitialState();
        this.loadState();
    }
    getInitialState() {
        return {
            currentStep: 'welcome',
            mode: null,
            sourceDirectory: null,
            destinationDirectory: null,
            selectedFiles: [],
            timeConfig: {
                days: 1,
                hoursPerDay: 8,
                customHours: {}
            },
            executionMode: null,
            changes: [],
            isProcessing: false,
            error: null,
            progress: {
                current: 0,
                total: 0,
                stage: ''
            }
        };
    }
    loadState() {
        const savedState = this.context.globalState.get('wakatimerpro.state');
        if (savedState) {
            this.state = { ...this.state, ...savedState };
        }
    }
    saveState() {
        this.context.globalState.update('wakatimerpro.state', this.state);
    }
    getState() {
        return { ...this.state };
    }
    updateState(updates) {
        this.state = { ...this.state, ...updates };
        this.saveState();
        this.onStateChangeEmitter.fire(this.state);
    }
    resetState() {
        this.state = this.getInitialState();
        this.saveState();
        this.onStateChangeEmitter.fire(this.state);
    }
    setCurrentStep(step) {
        this.updateState({ currentStep: step });
    }
    setMode(mode) {
        this.updateState({ mode });
    }
    setDirectories(source, destination) {
        this.updateState({
            sourceDirectory: source,
            destinationDirectory: destination
        });
    }
    setSelectedFiles(files) {
        this.updateState({ selectedFiles: files });
    }
    setTimeConfig(config) {
        this.updateState({
            timeConfig: { ...this.state.timeConfig, ...config }
        });
    }
    setExecutionMode(mode) {
        this.updateState({ executionMode: mode });
    }
    setChanges(changes) {
        this.updateState({ changes });
    }
    setProcessing(isProcessing) {
        this.updateState({ isProcessing });
    }
    setError(error) {
        this.updateState({ error });
    }
    setProgress(progress) {
        this.updateState({
            progress: { ...this.state.progress, ...progress }
        });
    }
}
exports.StateManager = StateManager;
//# sourceMappingURL=StateManager.js.map