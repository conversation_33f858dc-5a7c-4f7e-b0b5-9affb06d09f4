"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WakatimerProProvider = void 0;
const vscode = __importStar(require("vscode"));
const WelcomeView_1 = require("../views/WelcomeView");
const ModeSelectionView_1 = require("../views/ModeSelectionView");
const DirectorySelectionView_1 = require("../views/DirectorySelectionView");
const FileCopyView_1 = require("../views/FileCopyView");
const TimeConfigView_1 = require("../views/TimeConfigView");
const ExecutionModeView_1 = require("../views/ExecutionModeView");
const ProcessingView_1 = require("../views/ProcessingView");
const PreviewView_1 = require("../views/PreviewView");
const ExecutionView_1 = require("../views/ExecutionView");
const CompleteView_1 = require("../views/CompleteView");
class WakatimerProProvider {
    _onDidChangeTreeData = new vscode.EventEmitter();
    onDidChangeTreeData = this._onDidChangeTreeData.event;
    context;
    stateManager;
    currentView;
    constructor(context, stateManager) {
        this.context = context;
        this.stateManager = stateManager;
        // Listen to state changes
        this.stateManager.onStateChange((state) => {
            this.updateCurrentView(state);
            this.refresh();
        });
        this.updateCurrentView(this.stateManager.getState());
    }
    updateCurrentView(state) {
        switch (state.currentStep) {
            case 'welcome':
                this.currentView = new WelcomeView_1.WelcomeView(this.stateManager);
                break;
            case 'mode-selection':
                this.currentView = new ModeSelectionView_1.ModeSelectionView(this.stateManager);
                break;
            case 'directory-selection':
                this.currentView = new DirectorySelectionView_1.DirectorySelectionView(this.stateManager);
                break;
            case 'file-copy':
                this.currentView = new FileCopyView_1.FileCopyView(this.stateManager);
                break;
            case 'time-config':
                this.currentView = new TimeConfigView_1.TimeConfigView(this.stateManager);
                break;
            case 'execution-mode':
                this.currentView = new ExecutionModeView_1.ExecutionModeView(this.stateManager);
                break;
            case 'processing':
                this.currentView = new ProcessingView_1.ProcessingView(this.stateManager);
                break;
            case 'preview':
                this.currentView = new PreviewView_1.PreviewView(this.stateManager);
                break;
            case 'execution':
                this.currentView = new ExecutionView_1.ExecutionView(this.stateManager);
                break;
            case 'complete':
                this.currentView = new CompleteView_1.CompleteView(this.stateManager);
                break;
            default:
                this.currentView = new WelcomeView_1.WelcomeView(this.stateManager);
        }
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    async getChildren(element) {
        if (!element) {
            // Return root items from current view
            return this.currentView ? await this.currentView.getItems() : [];
        }
        // Handle child items if needed
        return [];
    }
    async handleFolderSelection(folderUri) {
        if (this.currentView && this.currentView.handleFolderSelection) {
            await this.currentView.handleFolderSelection(folderUri);
        }
    }
}
exports.WakatimerProProvider = WakatimerProProvider;
//# sourceMappingURL=WakatimerProProvider.js.map