"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeConfigView = void 0;
const vscode = __importStar(require("vscode"));
class TimeConfigView {
    stateManager;
    showAdvanced = false;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        const state = this.stateManager.getState();
        // Title
        const titleItem = new vscode.TreeItem('Configure Time Settings', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Set duration and schedule';
        titleItem.iconPath = new vscode.ThemeIcon('clock');
        items.push(titleItem);
        // Spacer
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        // Days configuration
        const daysItem = new vscode.TreeItem(`📅 Days: ${state.timeConfig.days}`, vscode.TreeItemCollapsibleState.None);
        daysItem.description = 'Number of days to simulate';
        daysItem.iconPath = new vscode.ThemeIcon('calendar');
        daysItem.command = {
            command: 'wakatimerpro.configureDays',
            title: 'Configure Days'
        };
        items.push(daysItem);
        // Hours per day configuration
        const hoursItem = new vscode.TreeItem(`⏰ Hours per day: ${state.timeConfig.hoursPerDay}`, vscode.TreeItemCollapsibleState.None);
        hoursItem.description = 'Working hours per day';
        hoursItem.iconPath = new vscode.ThemeIcon('watch');
        hoursItem.command = {
            command: 'wakatimerpro.configureHours',
            title: 'Configure Hours'
        };
        items.push(hoursItem);
        // Advanced options (only show if more than 1 day)
        if (state.timeConfig.days > 1) {
            const advancedItem = new vscode.TreeItem(this.showAdvanced ? '🔽 Advanced Options' : '▶️ Advanced Options', vscode.TreeItemCollapsibleState.None);
            advancedItem.description = 'Customize hours per day';
            advancedItem.iconPath = new vscode.ThemeIcon('settings-gear');
            advancedItem.command = {
                command: 'wakatimerpro.toggleTimeAdvanced',
                title: 'Toggle Advanced Time Options'
            };
            items.push(advancedItem);
            if (this.showAdvanced) {
                items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
                const headerItem = new vscode.TreeItem('Custom Hours per Day:', vscode.TreeItemCollapsibleState.None);
                headerItem.iconPath = new vscode.ThemeIcon('list-unordered');
                items.push(headerItem);
                // Show each day with custom hours
                for (let day = 1; day <= state.timeConfig.days; day++) {
                    const customHours = state.timeConfig.customHours[day] || state.timeConfig.hoursPerDay;
                    const dayItem = new vscode.TreeItem(`Day ${day}: ${customHours} hours`, vscode.TreeItemCollapsibleState.None);
                    dayItem.description = `Click to modify`;
                    dayItem.iconPath = new vscode.ThemeIcon('edit');
                    dayItem.command = {
                        command: 'wakatimerpro.configureCustomHours',
                        title: 'Configure Custom Hours',
                        arguments: [day]
                    };
                    items.push(dayItem);
                }
            }
        }
        // Summary
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const totalHours = this.calculateTotalHours(state.timeConfig);
        const totalChanges = Math.floor((totalHours * 60) / 1.5); // 90 seconds per change
        const summaryItem = new vscode.TreeItem('📊 Summary', vscode.TreeItemCollapsibleState.None);
        summaryItem.description = `${totalHours}h total, ~${totalChanges} changes`;
        summaryItem.iconPath = new vscode.ThemeIcon('graph');
        summaryItem.tooltip = `Total hours: ${totalHours}\nEstimated changes: ${totalChanges}\n(One change every 90 seconds)`;
        items.push(summaryItem);
        // Next button
        const nextItem = new vscode.TreeItem('✅ Next', vscode.TreeItemCollapsibleState.None);
        nextItem.description = 'Continue to execution mode';
        nextItem.iconPath = new vscode.ThemeIcon('arrow-right');
        nextItem.command = {
            command: 'wakatimerpro.proceedFromTimeConfig',
            title: 'Proceed from Time Config'
        };
        items.push(nextItem);
        // Back button
        items.push(new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None));
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to previous step';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: [state.mode === 'copy-and-edit' ? 'file-copy' : 'directory-selection']
        };
        items.push(backItem);
        return items;
    }
    calculateTotalHours(timeConfig) {
        let total = 0;
        for (let day = 1; day <= timeConfig.days; day++) {
            total += timeConfig.customHours[day] || timeConfig.hoursPerDay;
        }
        return total;
    }
}
exports.TimeConfigView = TimeConfigView;
//# sourceMappingURL=TimeConfigView.js.map