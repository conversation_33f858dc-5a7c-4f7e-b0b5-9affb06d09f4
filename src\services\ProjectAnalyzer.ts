import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface ProjectStructure {
    files: string[];
    directories: string[];
    tree: string;
    summary: {
        totalFiles: number;
        fileTypes: { [extension: string]: number };
        languages: string[];
    };
}

export class ProjectAnalyzer {
    private static readonly IGNORED_DIRS = [
        'node_modules', '.git', '.vscode', 'dist', 'out', 'build', 
        '.vscode-test', 'coverage', '.nyc_output', 'target', 'bin', 'obj'
    ];

    private static readonly IGNORED_FILES = [
        '.gitignore', '.vscodeignore', 'package-lock.json', 'yarn.lock',
        '.DS_Store', 'Thumbs.db', '*.log', '*.tmp'
    ];

    async analyzeProject(rootPath: string): Promise<ProjectStructure> {
        const files: string[] = [];
        const directories: string[] = [];
        
        await this.scanDirectory(rootPath, rootPath, files, directories);
        
        const tree = await this.generateTree(rootPath);
        const summary = this.generateSummary(files);

        return {
            files,
            directories,
            tree,
            summary
        };
    }

    private async scanDirectory(
        currentPath: string, 
        rootPath: string, 
        files: string[], 
        directories: string[]
    ): Promise<void> {
        try {
            const items = await fs.promises.readdir(currentPath, { withFileTypes: true });

            for (const item of items) {
                const fullPath = path.join(currentPath, item.name);
                const relativePath = path.relative(rootPath, fullPath);

                if (item.isDirectory()) {
                    if (!this.shouldIgnoreDirectory(item.name)) {
                        directories.push(relativePath);
                        await this.scanDirectory(fullPath, rootPath, files, directories);
                    }
                } else {
                    if (!this.shouldIgnoreFile(item.name)) {
                        files.push(relativePath);
                    }
                }
            }
        } catch (error) {
            console.error(`Error scanning directory ${currentPath}:`, error);
        }
    }

    private shouldIgnoreDirectory(dirName: string): boolean {
        return ProjectAnalyzer.IGNORED_DIRS.includes(dirName) || dirName.startsWith('.');
    }

    private shouldIgnoreFile(fileName: string): boolean {
        if (fileName.startsWith('.') && fileName !== '.env') {
            return true;
        }
        
        return ProjectAnalyzer.IGNORED_FILES.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(fileName);
            }
            return fileName === pattern;
        });
    }

    private async generateTree(rootPath: string, maxDepth: number = 3): Promise<string> {
        const tree: string[] = [];
        await this.buildTree(rootPath, rootPath, '', 0, maxDepth, tree);
        return tree.join('\n');
    }

    private async buildTree(
        currentPath: string,
        rootPath: string,
        prefix: string,
        depth: number,
        maxDepth: number,
        tree: string[]
    ): Promise<void> {
        if (depth > maxDepth) {
            return;
        }

        try {
            const items = await fs.promises.readdir(currentPath, { withFileTypes: true });
            const filteredItems = items.filter(item => {
                if (item.isDirectory()) {
                    return !this.shouldIgnoreDirectory(item.name);
                } else {
                    return !this.shouldIgnoreFile(item.name);
                }
            });

            filteredItems.sort((a, b) => {
                // Directories first, then files
                if (a.isDirectory() && !b.isDirectory()) return -1;
                if (!a.isDirectory() && b.isDirectory()) return 1;
                return a.name.localeCompare(b.name);
            });

            for (let i = 0; i < filteredItems.length; i++) {
                const item = filteredItems[i];
                const isLast = i === filteredItems.length - 1;
                const currentPrefix = isLast ? '└── ' : '├── ';
                const nextPrefix = isLast ? '    ' : '│   ';

                tree.push(`${prefix}${currentPrefix}${item.name}`);

                if (item.isDirectory()) {
                    const fullPath = path.join(currentPath, item.name);
                    await this.buildTree(
                        fullPath,
                        rootPath,
                        prefix + nextPrefix,
                        depth + 1,
                        maxDepth,
                        tree
                    );
                }
            }
        } catch (error) {
            console.error(`Error building tree for ${currentPath}:`, error);
        }
    }

    private generateSummary(files: string[]): ProjectStructure['summary'] {
        const fileTypes: { [extension: string]: number } = {};
        const languages = new Set<string>();

        for (const file of files) {
            const ext = path.extname(file).toLowerCase();
            fileTypes[ext] = (fileTypes[ext] || 0) + 1;

            // Map extensions to languages
            const language = this.getLanguageFromExtension(ext);
            if (language) {
                languages.add(language);
            }
        }

        return {
            totalFiles: files.length,
            fileTypes,
            languages: Array.from(languages)
        };
    }

    private getLanguageFromExtension(ext: string): string | null {
        const languageMap: { [key: string]: string } = {
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React',
            '.tsx': 'React TypeScript',
            '.py': 'Python',
            '.java': 'Java',
            '.c': 'C',
            '.cpp': 'C++',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.less': 'Less',
            '.json': 'JSON',
            '.xml': 'XML',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.md': 'Markdown',
            '.sql': 'SQL',
            '.sh': 'Shell',
            '.ps1': 'PowerShell',
            '.dockerfile': 'Docker',
            '.vue': 'Vue',
            '.svelte': 'Svelte'
        };

        return languageMap[ext] || null;
    }

    async getFileContent(filePath: string): Promise<string> {
        try {
            return await fs.promises.readFile(filePath, 'utf-8');
        } catch (error) {
            console.error(`Error reading file ${filePath}:`, error);
            return '';
        }
    }

    async getMultipleFileContents(rootPath: string, relativePaths: string[]): Promise<{ [filePath: string]: string }> {
        const contents: { [filePath: string]: string } = {};

        for (const relativePath of relativePaths) {
            const fullPath = path.join(rootPath, relativePath);
            try {
                const content = await this.getFileContent(fullPath);
                contents[relativePath] = content;
            } catch (error) {
                console.error(`Error reading file ${relativePath}:`, error);
                contents[relativePath] = `// Error reading file: ${error}`;
            }
        }

        return contents;
    }
}
