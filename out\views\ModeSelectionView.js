"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModeSelectionView = void 0;
const vscode = __importStar(require("vscode"));
class ModeSelectionView {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    async getItems() {
        const items = [];
        // Title
        const titleItem = new vscode.TreeItem('Choose Working Mode', vscode.TreeItemCollapsibleState.None);
        titleItem.description = 'Select how you want to work with your files';
        titleItem.iconPath = new vscode.ThemeIcon('gear');
        items.push(titleItem);
        // Spacer
        const spacerItem = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        items.push(spacerItem);
        // Edit in place option
        const editInPlaceItem = new vscode.TreeItem('📝 Edit in Place', vscode.TreeItemCollapsibleState.None);
        editInPlaceItem.description = 'Modify files in current workspace';
        editInPlaceItem.iconPath = new vscode.ThemeIcon('edit');
        editInPlaceItem.tooltip = 'Work directly with files in your current workspace. Changes will be applied to existing files.';
        editInPlaceItem.command = {
            command: 'wakatimerpro.selectMode',
            title: 'Select Edit in Place',
            arguments: ['edit-in-place']
        };
        items.push(editInPlaceItem);
        // Copy and edit option
        const copyAndEditItem = new vscode.TreeItem('📁 Make a Copy and Edit', vscode.TreeItemCollapsibleState.None);
        copyAndEditItem.description = 'Copy files to new location first';
        copyAndEditItem.iconPath = new vscode.ThemeIcon('copy');
        copyAndEditItem.tooltip = 'Copy files from a source directory to a destination directory, then apply changes to the copies.';
        copyAndEditItem.command = {
            command: 'wakatimerpro.selectMode',
            title: 'Select Copy and Edit',
            arguments: ['copy-and-edit']
        };
        items.push(copyAndEditItem);
        // Back button
        const spacer2Item = new vscode.TreeItem('', vscode.TreeItemCollapsibleState.None);
        items.push(spacer2Item);
        const backItem = new vscode.TreeItem('← Back', vscode.TreeItemCollapsibleState.None);
        backItem.description = 'Return to welcome screen';
        backItem.iconPath = new vscode.ThemeIcon('arrow-left');
        backItem.command = {
            command: 'wakatimerpro.goBack',
            title: 'Go Back',
            arguments: ['welcome']
        };
        items.push(backItem);
        // Register commands
        this.registerCommands();
        return items;
    }
    registerCommands() {
        vscode.commands.registerCommand('wakatimerpro.selectMode', (mode) => {
            this.stateManager.setMode(mode);
            this.stateManager.setCurrentStep('directory-selection');
        });
        vscode.commands.registerCommand('wakatimerpro.goBack', (step) => {
            this.stateManager.setCurrentStep(step);
        });
    }
}
exports.ModeSelectionView = ModeSelectionView;
//# sourceMappingURL=ModeSelectionView.js.map