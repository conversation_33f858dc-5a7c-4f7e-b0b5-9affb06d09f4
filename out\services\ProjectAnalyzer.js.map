{"version": 3, "file": "ProjectAnalyzer.js", "sourceRoot": "", "sources": ["../../src/services/ProjectAnalyzer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAyB;AACzB,2CAA6B;AAa7B,MAAa,eAAe;IAChB,MAAM,CAAU,YAAY,GAAG;QACnC,cAAc,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;QACzD,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK;KACpE,CAAC;IAEM,MAAM,CAAU,aAAa,GAAG;QACpC,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,WAAW;QAC/D,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO;KAC7C,CAAC;IAEF,KAAK,CAAC,cAAc,CAAC,QAAgB;QACjC,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAEjE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAE5C,OAAO;YACH,KAAK;YACL,WAAW;YACX,IAAI;YACJ,OAAO;SACV,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,aAAa,CACvB,WAAmB,EACnB,QAAgB,EAChB,KAAe,EACf,WAAqB;QAErB,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAE9E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAEvD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACzC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC/B,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;oBACrE,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACpC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC7B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,OAAe;QACzC,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACrF,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAChD,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,QAAQ,KAAK,OAAO,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,WAAmB,CAAC;QAC7D,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,SAAS,CACnB,WAAmB,EACnB,QAAgB,EAChB,MAAc,EACd,KAAa,EACb,QAAgB,EAChB,IAAc;QAEd,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9E,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7C,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxB,gCAAgC;gBAChC,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAClD,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBAC9B,MAAM,MAAM,GAAG,CAAC,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC9C,MAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC/C,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;gBAE5C,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAEnD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnD,MAAM,IAAI,CAAC,SAAS,CAChB,QAAQ,EACR,QAAQ,EACR,MAAM,GAAG,UAAU,EACnB,KAAK,GAAG,CAAC,EACT,QAAQ,EACR,IAAI,CACP,CAAC;gBACN,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,KAAe;QACnC,MAAM,SAAS,GAAoC,EAAE,CAAC;QACtD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAEpC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAE3C,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;YACpD,IAAI,QAAQ,EAAE,CAAC;gBACX,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;QACL,CAAC;QAED,OAAO;YACH,UAAU,EAAE,KAAK,CAAC,MAAM;YACxB,SAAS;YACT,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;SACnC,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,GAAW;QACxC,MAAM,WAAW,GAA8B;YAC3C,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,kBAAkB;YAC1B,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,QAAQ;YACf,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,YAAY;YACpB,aAAa,EAAE,QAAQ;YACvB,MAAM,EAAE,KAAK;YACb,SAAS,EAAE,QAAQ;SACtB,CAAC;QAEF,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACjC,IAAI,CAAC;YACD,OAAO,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAAgB,EAAE,aAAuB;QACnE,MAAM,QAAQ,GAAmC,EAAE,CAAC;QAEpD,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACnD,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACpD,QAAQ,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5D,QAAQ,CAAC,YAAY,CAAC,GAAG,0BAA0B,KAAK,EAAE,CAAC;YAC/D,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;;AA5NL,0CA6NC"}