"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeApplicator = void 0;
const vscode = __importStar(require("vscode"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class ChangeApplicator {
    isPaused = false;
    isStopped = false;
    async applyChanges(changes, rootPath, progressCallback, pauseCheckCallback) {
        let appliedChanges = 0;
        const totalChanges = changes.length;
        this.isPaused = false;
        this.isStopped = false;
        for (let i = 0; i < changes.length; i++) {
            // Check if paused or stopped
            if (this.isStopped) {
                break;
            }
            while (this.isPaused || (pauseCheckCallback && pauseCheckCallback())) {
                await this.sleep(1000); // Check every second
                if (this.isStopped) {
                    break;
                }
            }
            if (this.isStopped) {
                break;
            }
            const change = changes[i];
            if (progressCallback) {
                progressCallback(i + 1, totalChanges, `Verifying change ${change.id}...`);
            }
            // Re-verify change before applying (in case files were modified during pause)
            try {
                const isStillValid = await this.verifyChangeCanBeApplied(change, rootPath);
                if (!isStillValid) {
                    console.warn(`Change ${change.id} is no longer valid, skipping...`);
                    continue;
                }
            }
            catch (error) {
                console.error(`Error verifying change ${change.id}:`, error);
                continue;
            }
            if (progressCallback) {
                progressCallback(i + 1, totalChanges, `Applying change ${change.id}: ${change.description}`);
            }
            try {
                await this.applyChange(change, rootPath);
                appliedChanges++;
                // Wait 90 seconds between changes (except for the last one)
                if (i < changes.length - 1) {
                    await this.sleep(90000); // 90 seconds
                }
            }
            catch (error) {
                console.error(`Error applying change ${change.id}:`, error);
                // Continue with next change instead of failing completely
            }
        }
        return {
            success: !this.isStopped || appliedChanges === totalChanges,
            appliedChanges,
            totalChanges,
            error: this.isStopped ? 'Process was stopped by user' : undefined
        };
    }
    async applyChange(change, rootPath) {
        const filePath = path.join(rootPath, change.file);
        // Open the file in the editor
        await this.openFileInEditor(filePath);
        switch (change.type) {
            case 'addition':
                await this.createNewFile(filePath, change);
                break;
            case 'modification':
                await this.modifyExistingFile(filePath, change);
                break;
            case 'deletion':
                await this.deleteFile(filePath);
                break;
        }
        // Save the file
        const document = await vscode.workspace.openTextDocument(filePath);
        await document.save();
    }
    async openFileInEditor(filePath) {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            await vscode.window.showTextDocument(document, { preview: false });
        }
        catch (error) {
            // File might not exist yet (for new files)
            console.log(`File ${filePath} not found, will be created`);
        }
    }
    async createNewFile(filePath, change) {
        // Ensure directory exists
        const dir = path.dirname(filePath);
        await fs.promises.mkdir(dir, { recursive: true });
        // Generate content from diff
        const content = this.generateContentFromDiff(change.hunks, '');
        // Write file
        await fs.promises.writeFile(filePath, content, 'utf-8');
    }
    async modifyExistingFile(filePath, change) {
        // Read existing content
        const existingContent = await fs.promises.readFile(filePath, 'utf-8');
        // Apply diff
        const newContent = this.applyDiffToContent(existingContent, change.hunks);
        // Write modified content
        await fs.promises.writeFile(filePath, newContent, 'utf-8');
    }
    async deleteFile(filePath) {
        try {
            await fs.promises.unlink(filePath);
        }
        catch (error) {
            console.error(`Error deleting file ${filePath}:`, error);
        }
    }
    generateContentFromDiff(hunks, baseContent) {
        const lines = [];
        for (const hunk of hunks) {
            for (const line of hunk.lines) {
                if (line.type === 'addition' || line.type === 'context') {
                    lines.push(line.content);
                }
            }
        }
        return lines.join('\n');
    }
    applyDiffToContent(content, hunks) {
        const lines = content.split('\n');
        let offset = 0;
        for (const hunk of hunks) {
            const startLine = hunk.oldStart - 1 + offset; // Convert to 0-based index
            let currentLine = startLine;
            let deletions = 0;
            let additions = 0;
            // First pass: handle deletions
            for (const diffLine of hunk.lines) {
                if (diffLine.type === 'deletion') {
                    lines.splice(currentLine, 1);
                    deletions++;
                }
                else if (diffLine.type === 'context') {
                    currentLine++;
                }
                else if (diffLine.type === 'addition') {
                    // We'll handle additions in the second pass
                }
            }
            // Second pass: handle additions
            currentLine = startLine;
            for (const diffLine of hunk.lines) {
                if (diffLine.type === 'addition') {
                    lines.splice(currentLine, 0, diffLine.content);
                    currentLine++;
                    additions++;
                }
                else if (diffLine.type === 'context') {
                    currentLine++;
                }
            }
            // Update offset for subsequent hunks
            offset += additions - deletions;
        }
        return lines.join('\n');
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    pause() {
        this.isPaused = true;
    }
    resume() {
        this.isPaused = false;
    }
    stop() {
        this.isStopped = true;
        this.isPaused = false;
    }
    isPausedState() {
        return this.isPaused;
    }
    isStoppedState() {
        return this.isStopped;
    }
    async verifyChangeCanBeApplied(change, rootPath) {
        const filePath = path.join(rootPath, change.file);
        try {
            if (change.type === 'addition') {
                // For new files, check that the file doesn't already exist
                try {
                    await fs.promises.access(filePath);
                    return false; // File already exists
                }
                catch {
                    return true; // File doesn't exist, can create it
                }
            }
            if (change.type === 'deletion') {
                // For deletions, check that the file exists
                try {
                    await fs.promises.access(filePath);
                    return true; // File exists, can delete it
                }
                catch {
                    return false; // File doesn't exist
                }
            }
            if (change.type === 'modification') {
                // For modifications, check that the file exists and the hunks can be applied
                try {
                    const content = await fs.promises.readFile(filePath, 'utf-8');
                    return this.canApplyHunksToContent(content, change.hunks);
                }
                catch {
                    return false; // File doesn't exist or can't be read
                }
            }
            return false;
        }
        catch (error) {
            console.error(`Error verifying change ${change.id}:`, error);
            return false;
        }
    }
    canApplyHunksToContent(content, hunks) {
        const lines = content.split('\n');
        for (const hunk of hunks) {
            const startLine = hunk.oldStart - 1; // Convert to 0-based index
            // Check if the hunk can be applied at the expected location
            if (startLine < 0 || startLine >= lines.length) {
                return false;
            }
            let currentLine = startLine;
            for (const diffLine of hunk.lines) {
                if (diffLine.type === 'context' || diffLine.type === 'deletion') {
                    if (currentLine >= lines.length || lines[currentLine] !== diffLine.content) {
                        return false; // Context doesn't match
                    }
                    if (diffLine.type === 'context') {
                        currentLine++;
                    }
                }
            }
        }
        return true;
    }
}
exports.ChangeApplicator = ChangeApplicator;
//# sourceMappingURL=ChangeApplicator.js.map